<?php

namespace App\Exports;

use App\Models\DataSparepart;
use App\Models\RiwayatStokKantor;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class StokSparepartExport implements FromView, ShouldAutoSize
{
    protected $tanggal_awal;
    protected $tanggal_akhir;
    protected $kapal_id;
    protected $isAdminKapal;

    public function __construct($tanggal_awal, $tanggal_akhir, $kapal_id = null, $isAdminKapal = false)
    {
        $this->tanggal_awal = $tanggal_awal;
        $this->tanggal_akhir = $tanggal_akhir;
        $this->kapal_id = $kapal_id;
        $this->isAdminKapal = $isAdminKapal;
    }

    public function view(): View
    {
        $spareparts = DataSparepart::all();
        $data = [];

        foreach ($spareparts as $sparepart) {
            // Untuk admin_kapal, hanya tampilkan data kapal
            if ($this->isAdminKapal) {
                // Hanya hitung data kapal
                $stok_awal_kantor = 0;
                $masuk_kantor = 0;
                $keluar_kantor = 0;
                $total_kantor = 0;
            } else {
                // Stok Awal Kantor (akumulasi semua transaksi sebelum tanggal awal yang dipilih)
                $stok_awal_masuk = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                    ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                    ->where('riwayat_stok_kantor.jenis_transaksi', 'masuk')
                    ->join('pembelian_kantor', 'riwayat_stok_kantor.id_referensi', '=', 'pembelian_kantor.id')
                    ->whereDate('pembelian_kantor.tanggal', '<', date('Y-m-d', strtotime($this->tanggal_awal)))
                    ->sum('riwayat_stok_kantor.jumlah');

                $stok_awal_keluar = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                    ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                    ->where('riwayat_stok_kantor.jenis_transaksi', 'keluar')
                    ->join('pengeluaran_kantor', 'riwayat_stok_kantor.id_referensi', '=', 'pengeluaran_kantor.id')
                    ->whereDate('pengeluaran_kantor.tanggal', '<', date('Y-m-d', strtotime($this->tanggal_awal)))
                    ->sum('riwayat_stok_kantor.jumlah');

                $stok_awal_kantor = $stok_awal_masuk - $stok_awal_keluar;

                // Masuk Kantor (transaksi masuk dalam periode yang dipilih)
                $masuk_kantor = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                    ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                    ->where('riwayat_stok_kantor.jenis_transaksi', 'masuk')
                    ->join('pembelian_kantor', 'riwayat_stok_kantor.id_referensi', '=', 'pembelian_kantor.id')
                    ->whereDate('pembelian_kantor.tanggal', '>=', date('Y-m-d', strtotime($this->tanggal_awal)))
                    ->whereDate('pembelian_kantor.tanggal', '<=', date('Y-m-d', strtotime($this->tanggal_akhir)))
                    ->sum('riwayat_stok_kantor.jumlah');

                // Keluar Kantor (transaksi keluar dalam periode yang dipilih)
                $keluar_kantor = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                    ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                    ->where('riwayat_stok_kantor.jenis_transaksi', 'keluar')
                    ->join('pengeluaran_kantor', 'riwayat_stok_kantor.id_referensi', '=', 'pengeluaran_kantor.id')
                    ->whereDate('pengeluaran_kantor.tanggal', '>=', date('Y-m-d', strtotime($this->tanggal_awal)))
                    ->whereDate('pengeluaran_kantor.tanggal', '<=', date('Y-m-d', strtotime($this->tanggal_akhir)))
                    ->sum('riwayat_stok_kantor.jumlah');

                // Total Kantor
                $total_kantor = $stok_awal_kantor + $masuk_kantor - $keluar_kantor;
            }

            // Stok Awal Kapal
            $stok_awal_terima = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                ->where('riwayat_stok_kantor.jenis_transaksi', 'penerimaan_sparepart_kapal')
                ->join('penerimaan_kapal', 'riwayat_stok_kantor.id_referensi', '=', 'penerimaan_kapal.id')
                ->when($this->kapal_id, function($query) {
                    return $query->where('riwayat_stok_kantor.kapal_id', $this->kapal_id);
                })
                ->whereDate('penerimaan_kapal.tanggal', '<', date('Y-m-d', strtotime($this->tanggal_awal)))
                ->sum('riwayat_stok_kantor.jumlah');

            $stok_awal_pakai = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                ->where('riwayat_stok_kantor.jenis_transaksi', 'pemakaian_sparepart_kapal')
                ->join('pemakaian_kapal', 'riwayat_stok_kantor.id_referensi', '=', 'pemakaian_kapal.id')
                ->when($this->kapal_id, function($query) {
                    return $query->where('riwayat_stok_kantor.kapal_id', $this->kapal_id);
                })
                ->whereDate('pemakaian_kapal.tanggal', '<', date('Y-m-d', strtotime($this->tanggal_awal)))
                ->sum('riwayat_stok_kantor.jumlah');

            $stok_awal_kapal = $stok_awal_terima - $stok_awal_pakai;

            // Masuk Kapal
            $masuk_kapal = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                ->where('riwayat_stok_kantor.jenis_transaksi', 'penerimaan_sparepart_kapal')
                ->join('penerimaan_kapal', 'riwayat_stok_kantor.id_referensi', '=', 'penerimaan_kapal.id')
                ->when($this->kapal_id, function($query) {
                    return $query->where('riwayat_stok_kantor.kapal_id', $this->kapal_id);
                })
                ->whereDate('penerimaan_kapal.tanggal', '>=', date('Y-m-d', strtotime($this->tanggal_awal)))
                ->whereDate('penerimaan_kapal.tanggal', '<=', date('Y-m-d', strtotime($this->tanggal_akhir)))
                ->sum('riwayat_stok_kantor.jumlah');

            // Keluar Kapal
            $keluar_kapal = RiwayatStokKantor::select('riwayat_stok_kantor.*')
                ->where('riwayat_stok_kantor.id_barang', $sparepart->id)
                ->where('riwayat_stok_kantor.jenis_transaksi', 'pemakaian_sparepart_kapal')
                ->join('pemakaian_kapal', 'riwayat_stok_kantor.id_referensi', '=', 'pemakaian_kapal.id')
                ->when($this->kapal_id, function($query) {
                    return $query->where('riwayat_stok_kantor.kapal_id', $this->kapal_id);
                })
                ->whereDate('pemakaian_kapal.tanggal', '>=', date('Y-m-d', strtotime($this->tanggal_awal)))
                ->whereDate('pemakaian_kapal.tanggal', '<=', date('Y-m-d', strtotime($this->tanggal_akhir)))
                ->sum('riwayat_stok_kantor.jumlah');

            // Total Kapal
            $total_kapal = $stok_awal_kapal + $masuk_kapal - $keluar_kapal;

            // Total Stok
            $total_stok = $total_kantor + $total_kapal;

            // Untuk admin_kapal, tampilkan semua data (termasuk stok kosong) tapi kosongkan kolom opname
            if ($this->isAdminKapal) {
                $data[] = [
                    'nomor_seri' => $sparepart->nomor_seri,
                    'nama_barang' => $sparepart->nama_barang,
                    'satuan' => $sparepart->satuan,
                    'jenis' => $sparepart->jenis,
                    'stok_awal_kantor' => $stok_awal_kantor,
                    'masuk_kantor' => $masuk_kantor,
                    'keluar_kantor' => $keluar_kantor,
                    'total_kantor' => $total_kantor,
                    'stok_awal_kapal' => $stok_awal_kapal,
                    'masuk_kapal' => $masuk_kapal,
                    'keluar_kapal' => $keluar_kapal,
                    'total_kapal' => $total_kapal,
                    'total_stok' => $total_stok, // Tampilkan total stok untuk admin_kapal
                    'opname' => '' // Kosongkan opname untuk admin_kapal
                ];
            } else {
                // Untuk role lain, tampilkan semua data
                $data[] = [
                    'nomor_seri' => $sparepart->nomor_seri,
                    'nama_barang' => $sparepart->nama_barang,
                    'satuan' => $sparepart->satuan,
                    'jenis' => $sparepart->jenis,
                    'stok_awal_kantor' => $stok_awal_kantor,
                    'masuk_kantor' => $masuk_kantor,
                    'keluar_kantor' => $keluar_kantor,
                    'total_kantor' => $total_kantor,
                    'stok_awal_kapal' => $stok_awal_kapal,
                    'masuk_kapal' => $masuk_kapal,
                    'keluar_kapal' => $keluar_kapal,
                    'total_kapal' => $total_kapal,
                    'total_stok' => $total_stok,
                    'opname' => null
                ];
            }
        }

        return view('stok-sparepart.export', [
            'data' => $data,
            'tanggal_awal' => $this->tanggal_awal,
            'tanggal_akhir' => $this->tanggal_akhir,
            'isAdminKapal' => $this->isAdminKapal
        ]);
    }
} 