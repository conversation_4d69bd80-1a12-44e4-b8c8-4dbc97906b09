{"__meta": {"id": "X763af5a7b47f1adc20acb6abf7607e34", "datetime": "2025-07-20 07:31:36", "utime": 1752971496.14765, "method": "GET", "uri": "/stok-sparepart/export?kapal_id=4&date_range=01-01-2025+sampai+01-07-2025&tanggal_awal=2025-01-01&tanggal_akhir=2025-07-01&type=pdf", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752971491.823654, "end": 1752971496.147674, "duration": 4.324020147323608, "duration_str": "4.32s", "measures": [{"label": "Booting", "start": 1752971491.823654, "relative_start": 0, "end": **********.138026, "relative_end": **********.138026, "duration": 0.31437206268310547, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.138041, "relative_start": 0.31438708305358887, "end": 1752971496.147677, "relative_end": 2.86102294921875e-06, "duration": 4.009635925292969, "duration_str": "4.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 74804088, "peak_usage_str": "71MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "stok-sparepart.pdf", "param_count": null, "params": [], "start": **********.250835, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/stok-sparepart/pdf.blade.phpstok-sparepart.pdf", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fstok-sparepart%2Fpdf.blade.php&line=1", "ajax": false, "filename": "pdf.blade.php", "line": "?"}}]}, "route": {"uri": "GET stok-sparepart/export", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\StokSparepartController@export", "namespace": null, "prefix": "", "where": [], "as": "stok-sparepart.export", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=172\" onclick=\"\">app/Http/Controllers/StokSparepartController.php:172-247</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.005639999999999999, "accumulated_duration_str": "5.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.199028, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.203358, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 59.22}, {"sql": "select count(*) as aggregate from `kapal` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 875}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}], "start": **********.22629, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.22, "width_percent": 10.816}, {"sql": "select * from `kapal` where `id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 182}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2295802, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "StokSparepartController.php:182", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=182", "ajax": false, "filename": "StokSparepartController.php", "line": "182"}, "connection": "gema_kapal", "explain": null, "start_percent": 70.035, "width_percent": 4.965}, {"sql": "select `rsk`.`created_at` as `tanggal`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, `rsk`.`jenis_transaksi`, `rsk`.`jumlah`, `rsk`.`stok_sebelum`, `rsk`.`stok_setelah`, `rsk`.`keterangan` from `riwayat_stok_kantor` as `rsk` inner join `data_sparepart` as `ds` on `rsk`.`id_barang` = `ds`.`id` where `rsk`.`jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal') and `rsk`.`created_at` between '2025-01-01 00:00:00' and '2025-07-01 23:59:59' order by `rsk`.`created_at` desc", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", "2025-01-01 00:00:00", "2025-07-01 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 209}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.231773, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "StokSparepartController.php:209", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=209", "ajax": false, "filename": "StokSparepartController.php", "line": "209"}, "connection": "gema_kapal", "explain": null, "start_percent": 75, "width_percent": 25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/stok-sparepart/export?date_range=01-01-2025%20sampai%2001-07-2025&kapal_id=4&tanggal_akhir=2025-07-01&tanggal_awal=2025-01-01&type=pdf\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/stok-sparepart/export", "status_code": "<pre class=sf-dump id=sf-dump-1039123893 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1039123893\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/pdf", "request_query": "<pre class=sf-dump id=sf-dump-1348317101 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>kapal_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>date_range</span>\" => \"<span class=sf-dump-str title=\"28 characters\">01-01-2025 sampai 01-07-2025</span>\"\n  \"<span class=sf-dump-key>tanggal_awal</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-01</span>\"\n  \"<span class=sf-dump-key>tanggal_akhir</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-01</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pdf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348317101\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-965438970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-965438970\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-878087699 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://sikapal.test/stok-sparepart/export-form</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjMwOG5iZ1A5K2JrMFJqVjUzbzl0Vnc9PSIsInZhbHVlIjoiQlVwaXFUZThqdU8yRjA2V09LY3FGZ0pFQWZhMkkxNjUvUnBONTEvR0NJcDdwaU51M0c4eXcwRHVTTjhxd0NQRDU4Q3NqU1M5U3YxcVFDS1h0VlpMVW04ZUVzdHdJMDFrK3d6eDhNWVJIdXhvcm1KcDEwQmtSUFBZYTZUY0FLR28iLCJtYWMiOiI3YjE1NDNlMzA2YzkzOTliMGUzMzk4MTg4MTJlNmI1OWI4YzJiYjU1Yzk1NmQyZDA0NzgzMDQ2ODIxNTY4ODUzIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6Im1iNTdYUkVwdmJ1RHFUckx1MDhiOVE9PSIsInZhbHVlIjoiWFU5TzJxQUE4azV0L1NlZEZpd3JIMzd6WXJ1cHNkdkJNTlBObDhuNXBVS3FkQnNlMDZrR1F3WG56MnZkbXpIa2JlWUZud0U1RzZuY2Q0QmFLYi8ya1JlVksycjF1OG5adlkzYWdISG5SR0k1VmxJNW45T01tNG9yZ3IzUW9UdzkiLCJtYWMiOiJjZWNiMzkzM2Y1MTZlYTgwODA2OTAyMmZlYzgwM2NiMDFkM2VjZjQ3OGFmNmFlYzk4ZWM2NDcxNTdiOWU5MDUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878087699\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-431097970 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xd646bANpl7rqaFjqnXFGwoyUtkp1VrQmXt91Gb7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431097970\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1721052672 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"89 characters\">attachment; filename=&quot;Laporan Stok Sparepart TB. GEMA 201 (01-01-2025 sd 01-07-2025).pdf&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">50612</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:31:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkF0QmJOK1lsYkM3M2lidUMzS2hya0E9PSIsInZhbHVlIjoiUWtDRVdGMGFMTCtQcXhoLzl6OVZVUEFOU2VqYlNRY3A1eHRzVkZ1ait0aDNyYXUyNGIzN0pUQm5DVEJnWURRZFBsbm14Q3RGbTJFVEVuVkUrWk9RZCtsVlNGTnZ1U3VRMnZ0cnZuZnJxb0g1amUrUFU5YXFVck5TYURQbGRrRGciLCJtYWMiOiIzYjFmMTdhNzE2OGIyYWU4MTczYTMxNWIxODljNTE3NmJhZDlmNjUyYzA2NDZiMzQ2ZDNjZjI1ODZiNjY1ZDBjIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:31:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IlRtMDllcmtMK0QycEkzc0ZCTG9OaFE9PSIsInZhbHVlIjoiMzZwWWZaaGFIUUlxcytlWHIrNEQ1MkhpbDZmUFJnZDN0K1cwTG1xV3p1TmZ5NjBTRWVwZXVzaThsVVpRaCtVVjkrdkJiTE9WMldTR0pRME8wZ1pET25CMDNXbTZZdHZHRE1HUmxnRXl2YW9GTUkrZmsyUFR6MFFGaVpBRmtRczUiLCJtYWMiOiI5YThiYmFkZTZjOTlkZTk3ODE1NmIxODc3ZTBmZjM3NGQ3NTM1ODkyN2ZhMTMyZWE0MWFmNmI1OWQyYTIwOTUxIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:31:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkF0QmJOK1lsYkM3M2lidUMzS2hya0E9PSIsInZhbHVlIjoiUWtDRVdGMGFMTCtQcXhoLzl6OVZVUEFOU2VqYlNRY3A1eHRzVkZ1ait0aDNyYXUyNGIzN0pUQm5DVEJnWURRZFBsbm14Q3RGbTJFVEVuVkUrWk9RZCtsVlNGTnZ1U3VRMnZ0cnZuZnJxb0g1amUrUFU5YXFVck5TYURQbGRrRGciLCJtYWMiOiIzYjFmMTdhNzE2OGIyYWU4MTczYTMxNWIxODljNTE3NmJhZDlmNjUyYzA2NDZiMzQ2ZDNjZjI1ODZiNjY1ZDBjIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:31:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IlRtMDllcmtMK0QycEkzc0ZCTG9OaFE9PSIsInZhbHVlIjoiMzZwWWZaaGFIUUlxcytlWHIrNEQ1MkhpbDZmUFJnZDN0K1cwTG1xV3p1TmZ5NjBTRWVwZXVzaThsVVpRaCtVVjkrdkJiTE9WMldTR0pRME8wZ1pET25CMDNXbTZZdHZHRE1HUmxnRXl2YW9GTUkrZmsyUFR6MFFGaVpBRmtRczUiLCJtYWMiOiI5YThiYmFkZTZjOTlkZTk3ODE1NmIxODc3ZTBmZjM3NGQ3NTM1ODkyN2ZhMTMyZWE0MWFmNmI1OWQyYTIwOTUxIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:31:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721052672\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1244156326 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"154 characters\">http://sikapal.test/stok-sparepart/export?date_range=01-01-2025%20sampai%2001-07-2025&amp;kapal_id=4&amp;tanggal_akhir=2025-07-01&amp;tanggal_awal=2025-01-01&amp;type=pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1244156326\", {\"maxDepth\":0})</script>\n"}}