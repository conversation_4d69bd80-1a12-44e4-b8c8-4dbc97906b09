<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChecklistDataEngine extends Model
{
    protected $table = 'checklist_data_engine';
    
    protected $fillable = [
        'id',
        'jenis',
        'sistem',
        'item_pemeriksaan',
        'waktu'
    ];

    public static function getNotifications($running_hours_ps, $running_hours_sb, $kapal_id = null)
    {
        $notifications = [];

        // Ambil semua data checklist dan urutkan berdasarkan waktu
        $checklists = self::orderBy('waktu', 'asc')->get();

        foreach ($checklists as $checklist) {
            // Logika khusus untuk Penggantian Oli Powerpack (ID 174)
            if ($checklist->id == 174) {
                $powerpakNotification = self::getPowerpakNotification($checklist, $running_hours_ps, $running_hours_sb, $kapal_id);
                if ($powerpakNotification) {
                    $notifications[] = $powerpakNotification;
                }
                continue;
            }

            // Konversi waktu ke jam jika dalam format lain
            $hours = self::convertToHours($checklist->waktu);

            // Tentukan running hours berdasarkan jenis
            $running_hours = null;
            $engine = null;

            if (stripos($checklist->jenis, 'PORTSIDE') !== false) {
                $running_hours = $running_hours_ps;
                $engine = 'PORTSIDE';
            } elseif (stripos($checklist->jenis, 'STARBOARDSIDE') !== false) {
                $running_hours = $running_hours_sb;
                $engine = 'STARBOARDSIDE';
            } else {
                // Untuk jenis lain, gunakan running hours yang lebih tinggi
                $running_hours = max($running_hours_ps, $running_hours_sb);
                $engine = 'OTHER';
            }

            if ($hours) {
                // Cek apakah checklist ini perlu muncul berdasarkan running hours terakhir saat dikerjakan
                $shouldShow = self::shouldShowChecklist($checklist->id, $running_hours, $hours, $kapal_id);

                if ($shouldShow) {
                    // Hitung berapa kali interval sudah terlewati
                    $cycles = floor($running_hours / $hours);

                    $notifications[] = [
                        'id' => $checklist->id,
                        'engine' => $engine,
                        'jenis' => $checklist->jenis,
                        'sistem' => $checklist->sistem,
                        'item_pemeriksaan' => $checklist->item_pemeriksaan,
                        'waktu' => $checklist->waktu,
                        'interval' => $hours,
                        'running_hours' => $running_hours,
                        'cycles' => $cycles // tambahkan informasi siklus
                    ];
                }
            } else {
                // Untuk item tanpa interval, tampilkan jika running hours > 0
                if ($running_hours > 0) {
                    $notifications[] = [
                        'id' => $checklist->id,
                        'engine' => $engine,
                        'jenis' => $checklist->jenis,
                        'sistem' => $checklist->sistem,
                        'item_pemeriksaan' => $checklist->item_pemeriksaan,
                        'waktu' => $checklist->waktu,
                        'interval' => null, // Tambahkan key interval dengan nilai null untuk konsistensi
                        'running_hours' => $running_hours
                    ];
                }
            }
        }

        // Urutkan notifikasi berdasarkan interval waktu
        return collect($notifications)->sortBy('interval')->values()->all();
    }

    /**
     * Logika khusus untuk Penggantian Oli Powerpack
     * 2x oli ganti mesin = 1x ganti oli powerpack (interval 800 jam)
     */
    private static function getPowerpakNotification($checklist, $running_hours_ps, $running_hours_sb, $kapal_id)
    {
        if (!$kapal_id) {
            return null;
        }

        // Hitung total penggantian oli mesin (ID 1 untuk PS dan ID 73 untuk SB)
        $oliMesinPsCount = \DB::table('hasil_pemeriksaan_engine')
            ->where('kapal_id', $kapal_id)
            ->where('item_id', 1) // GANTI OLI PORTSIDE
            ->where('kondisi', 'bagus')
            ->count();

        $oliMesinSbCount = \DB::table('hasil_pemeriksaan_engine')
            ->where('kapal_id', $kapal_id)
            ->where('item_id', 73) // GANTI OLI STARBOARDSIDE
            ->where('kondisi', 'bagus')
            ->count();

        // Hitung total penggantian oli powerpack yang sudah dilakukan
        $oliPowerpakCount = \DB::table('hasil_pemeriksaan_engine')
            ->where('kapal_id', $kapal_id)
            ->where('item_id', 174) // PENGGANTIAN OLI POWERPACK
            ->where('kondisi', 'bagus')
            ->count();

        // Total penggantian oli mesin dari kedua engine
        $totalOliMesinCount = $oliMesinPsCount + $oliMesinSbCount;

        // Hitung berapa kali oli powerpack seharusnya sudah diganti
        $expectedPowerpakCount = floor($totalOliMesinCount / 2);

        // Jika oli powerpack belum diganti sesuai dengan yang diharapkan, tampilkan notifikasi
        if ($expectedPowerpakCount > $oliPowerpakCount) {
            $running_hours = max($running_hours_ps, $running_hours_sb);

            return [
                'id' => $checklist->id,
                'engine' => 'SITUASIONAL',
                'jenis' => $checklist->jenis,
                'sistem' => $checklist->sistem,
                'item_pemeriksaan' => $checklist->item_pemeriksaan,
                'waktu' => $checklist->waktu,
                'interval' => 800,
                'running_hours' => $running_hours,
                'cycles' => $expectedPowerpakCount,
                'oli_mesin_count' => $totalOliMesinCount,
                'oli_powerpack_count' => $oliPowerpakCount,
                'expected_powerpack_count' => $expectedPowerpakCount
            ];
        }

        return null;
    }

    /**
     * Cek apakah checklist perlu ditampilkan berdasarkan running hours terakhir saat dikerjakan
     */
    private static function shouldShowChecklist($item_id, $current_running_hours, $interval_hours, $kapal_id)
    {
        if (!$kapal_id) {
            // Jika tidak ada kapal_id, gunakan logika lama
            $cycles = floor($current_running_hours / $interval_hours);
            return $cycles >= 1;
        }

        // Ambil checklist data untuk menentukan jenis engine
        $checklist = self::find($item_id);
        if (!$checklist) {
            return false;
        }

        // Ambil hasil pemeriksaan terakhir untuk item ini
        $lastResult = \DB::table('hasil_pemeriksaan_engine as hpe')
            ->join('histori_perjalanan_kapal as hpk', 'hpe.history_id', '=', 'hpk.id')
            ->where('hpe.kapal_id', $kapal_id)
            ->where('hpe.item_id', $item_id)
            ->where('hpe.kondisi', 'bagus')
            ->orderBy('hpe.created_at', 'desc')
            ->select('hpk.id as history_id', 'hpe.created_at')
            ->first();

        if (!$lastResult) {
            // Jika belum pernah dikerjakan, cek apakah sudah waktunya
            $cycles = floor($current_running_hours / $interval_hours);
            return $cycles >= 1;
        }

        // Hitung total running hours kapal saat checklist terakhir dikerjakan
        // Ambil total RH kapal saat ini, lalu kurangi dengan RH yang ditambahkan setelah checklist dikerjakan
        $kapal = \DB::table('kapal')->where('id', $kapal_id)->first();
        if (!$kapal) {
            return false;
        }

        $currentTotalRhPs = (int) $kapal->rh_me_ps;
        $currentTotalRhSb = (int) $kapal->rh_me_sb;

        // Hitung RH yang ditambahkan setelah checklist terakhir dikerjakan
        $rhAddedAfterLastCheck = \DB::table('histori_perjalanan_kapal')
            ->where('kapal_id', $kapal_id)
            ->where('id', '>', $lastResult->history_id)
            ->select(
                \DB::raw('COALESCE(SUM(rh_me_ps), 0) as total_ps'),
                \DB::raw('COALESCE(SUM(rh_me_sb), 0) as total_sb')
            )
            ->first();

        // Hitung RH saat checklist terakhir dikerjakan
        $rhPsAtLastCheck = $currentTotalRhPs - ($rhAddedAfterLastCheck->total_ps ?? 0);
        $rhSbAtLastCheck = $currentTotalRhSb - ($rhAddedAfterLastCheck->total_sb ?? 0);

        // Tentukan running hours yang relevan berdasarkan jenis checklist
        $last_relevant_rh = null;
        if (stripos($checklist->jenis, 'PORTSIDE') !== false) {
            $last_relevant_rh = $rhPsAtLastCheck;
        } elseif (stripos($checklist->jenis, 'STARBOARDSIDE') !== false) {
            $last_relevant_rh = $rhSbAtLastCheck;
        } else {
            // Untuk jenis lain, gunakan yang lebih tinggi
            $last_relevant_rh = max($rhPsAtLastCheck, $rhSbAtLastCheck);
        }

        // Hitung running hours berikutnya yang harus dicapai untuk checklist muncul lagi
        $last_cycles = floor($last_relevant_rh / $interval_hours);
        $next_required_rh = ($last_cycles + 1) * $interval_hours;

        // Checklist muncul jika current running hours sudah mencapai atau melewati next required
        return $current_running_hours >= $next_required_rh;
    }

    private static function convertToHours($waktu)
    {
        if (empty($waktu)) return null;

        // Jika sudah dalam format jam
        if (is_numeric($waktu)) {
            return (int) $waktu;
        }

        // Konversi dari format teks
        $waktu = strtoupper($waktu);
        
        if (strpos($waktu, 'HARI') !== false) {
            $days = (int) $waktu;
            return $days * 24; // konversi hari ke jam
        }
        
        if (strpos($waktu, 'TAHUN') !== false) {
            $years = (int) $waktu;
            return $years * 365 * 24; // konversi tahun ke jam
        }
        
        if (strpos($waktu, 'BULAN') !== false) {
            $months = (int) $waktu;
            return $months * 30 * 24; // konversi bulan ke jam
        }
        
        return null;
    }
} 