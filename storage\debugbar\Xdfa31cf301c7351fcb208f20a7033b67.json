{"__meta": {"id": "Xdfa31cf301c7351fcb208f20a7033b67", "datetime": "2025-07-20 08:04:45", "utime": 1752973485.649647, "method": "GET", "uri": "/checklist-engine/2NaDK50q4p", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[08:04:44] LOG.info: Kapal: {\n    \"id\": 4,\n    \"kapal\": {\n        \"id\": 4,\n        \"nama\": \"TB. GEMA 201\",\n        \"rh_me_ps\": \"12623\",\n        \"rh_me_sb\": \"12618\",\n        \"jenis_kapal_id\": 1,\n        \"created_at\": \"2024-12-16T09:41:48.000000Z\",\n        \"updated_at\": \"2025-07-14T12:43:33.000000Z\",\n        \"gt_nt\": \"204\\/62\",\n        \"port_of_registry\": \"SAMARINDA\",\n        \"tanda_pendaftaran\": \"2022 IIk No. 9810\\/L\",\n        \"call_sign\": \"YDC 6210\",\n        \"imo\": \"9982706\",\n        \"mesin\": \"MITSUBISHI 2X 759 KW\",\n        \"tanda_selar\": \"GT. 204 No. 7085\\/IIK\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.237852, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752973483.865229, "end": 1752973485.649671, "duration": 1.7844421863555908, "duration_str": "1.78s", "measures": [{"label": "Booting", "start": 1752973483.865229, "relative_start": 0, "end": **********.153195, "relative_end": **********.153195, "duration": 0.2879660129547119, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.15321, "relative_start": 0.2879810333251953, "end": 1752973485.649674, "relative_end": 2.86102294921875e-06, "duration": 1.4964640140533447, "duration_str": "1.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 33628728, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "checklist-engine.index", "param_count": null, "params": [], "start": **********.453414, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/checklist-engine/index.blade.phpchecklist-engine.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fchecklist-engine%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": 1752973485.605717, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": 1752973485.62509, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": 1752973485.626817, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": 1752973485.62795, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": 1752973485.641605, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET checklist-engine/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ChecklistEngineController@index", "namespace": null, "prefix": "", "where": [], "as": "checklist-engine.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=19\" onclick=\"\">app/Http/Controllers/ChecklistEngineController.php:19-133</a>"}, "queries": {"nb_statements": 344, "nb_visible_statements": 345, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09566000000000009, "accumulated_duration_str": "95.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 244 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.203792, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `kapal` where `kapal`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.20837, "duration": 0.0198, "duration_str": "19.8ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 20.698}, {"sql": "select * from `jenis_kapal` where `jenis_kapal`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2410371, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:34", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=34", "ajax": false, "filename": "ChecklistEngineController.php", "line": "34"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.698, "width_percent": 1.234}, {"sql": "select * from `histori_per<PERSON>lanan_kapal` where `kapal_id` = 4 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.245087, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:47", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=47", "ajax": false, "filename": "ChecklistEngineController.php", "line": "47"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.932, "width_percent": 0.753}, {"sql": "select `hpe`.*, `cde`.`item_pemeriksaan` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`history_id` = 24", "type": "query", "params": [], "bindings": [4, 24], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.247192, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:55", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=55", "ajax": false, "filename": "ChecklistEngineController.php", "line": "55"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.685, "width_percent": 0.627}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.248973, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.312, "width_percent": 0.627}, {"sql": "select `hpe`.*, `cde`.`item_pemeri<PERSON><PERSON>`, `hpk`.`tujuan`, `hpk`.`rh_me_ps`, `hpk`.`rh_me_sb` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON>n_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 order by `hpe`.`tanggal_pemeriksaan` desc, `hpe`.`created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.250769, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.939, "width_percent": 0.909}, {"sql": "select count(*) as aggregate from `checklist_engines` where `kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2540488, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:99", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=99", "ajax": false, "filename": "ChecklistEngineController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.848, "width_percent": 0.429}, {"sql": "select * from `checklist_data_engine` order by `waktu` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.255922, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:24", "source": {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=24", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.277, "width_percent": 0.659}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.261275, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.936, "width_percent": 0.408}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 42 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 42, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.26266, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.343, "width_percent": 0.324}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.263959, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.667, "width_percent": 0.366}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.265606, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.033, "width_percent": 0.24}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2668889, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.274, "width_percent": 0.251}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 44 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 44, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2680428, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.525, "width_percent": 0.261}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.269252, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.786, "width_percent": 0.345}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.270565, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.131, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.271874, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.33, "width_percent": 0.606}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 54 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 54, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.273507, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.936, "width_percent": 0.345}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.274847, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.281, "width_percent": 0.335}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.276175, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.615, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 58 limit 1", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.277375, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.814, "width_percent": 0.314}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 58 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 58, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.278615, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.128, "width_percent": 0.345}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.279961, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.473, "width_percent": 0.282}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.281207, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.755, "width_percent": 0.167}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.282358, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.922, "width_percent": 0.272}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 65 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 65, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.283532, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.194, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.284733, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.434, "width_percent": 0.293}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.286051, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.727, "width_percent": 0.188}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2872238, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.915, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 72 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 72, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.288387, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.176, "width_percent": 0.22}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2896008, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.396, "width_percent": 0.272}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.290821, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.668, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 87 limit 1", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.292007, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.866, "width_percent": 0.314}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 87 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 87, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.293272, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.18, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.294471, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.42, "width_percent": 0.282}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.295785, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.703, "width_percent": 0.167}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 88 limit 1", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2969558, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.87, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 88 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 88, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2981029, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.131, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.299334, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.372, "width_percent": 0.387}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3007, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.759, "width_percent": 0.335}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3021798, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.093, "width_percent": 0.251}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 94 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 94, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.303376, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.344, "width_percent": 0.261}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3046122, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.605, "width_percent": 0.293}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.305886, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.898, "width_percent": 0.167}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.30722, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.065, "width_percent": 0.387}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 96 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 96, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.308577, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.452, "width_percent": 0.282}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.309829, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.734, "width_percent": 0.345}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.311143, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.079, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 99 limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3123279, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.278, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 99 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 99, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3135438, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.539, "width_percent": 0.261}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3147688, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.801, "width_percent": 0.261}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.316003, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.062, "width_percent": 0.209}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 106 limit 1", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.317193, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.271, "width_percent": 0.251}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 106 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 106, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.318328, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.522, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.31951, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.762, "width_percent": 0.272}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3208039, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.034, "width_percent": 0.251}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 109 limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3220668, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.285, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 109 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 109, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3232, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.546, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.324388, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.787, "width_percent": 0.251}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.325589, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.038, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3268352, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.236, "width_percent": 0.303}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 112 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 112, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.328159, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.539, "width_percent": 0.335}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 118 limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.329516, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.874, "width_percent": 0.272}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 118 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 118, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.330675, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.146, "width_percent": 0.22}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 120 limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.331894, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.365, "width_percent": 0.355}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 120 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 120, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3332798, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.721, "width_percent": 0.251}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 123 limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.334589, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.972, "width_percent": 0.293}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 123 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 123, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.335768, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.264, "width_percent": 0.188}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.336958, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.452, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 131 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 131, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.338078, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.682, "width_percent": 0.178}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 134 limit 1", "type": "query", "params": [], "bindings": [134], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.339264, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.86, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 134 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 134, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.340395, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.121, "width_percent": 0.209}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 138 limit 1", "type": "query", "params": [], "bindings": [138], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.341694, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.331, "width_percent": 0.46}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 138 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 138, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3431492, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.791, "width_percent": 0.314}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 139 limit 1", "type": "query", "params": [], "bindings": [139], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.344498, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.104, "width_percent": 0.355}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 139 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 139, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.345806, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.46, "width_percent": 0.261}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 140 limit 1", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3470812, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.721, "width_percent": 0.303}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 140 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 140, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.348365, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.024, "width_percent": 0.293}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 141 limit 1", "type": "query", "params": [], "bindings": [141], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.349798, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.317, "width_percent": 0.345}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 141 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 141, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3510568, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.662, "width_percent": 0.24}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 143 limit 1", "type": "query", "params": [], "bindings": [143], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.35229, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.902, "width_percent": 0.251}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 143 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 143, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.353438, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.153, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3546488, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.393, "width_percent": 0.439}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356146, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.833, "width_percent": 0.355}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.35756, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.188, "width_percent": 0.272}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 18 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 18, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.358754, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.46, "width_percent": 0.272}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.360008, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.732, "width_percent": 0.335}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 22 limit 1", "type": "query", "params": [], "bindings": [4, 22], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.361359, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.066, "width_percent": 0.24}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 50 limit 1", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.362754, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.307, "width_percent": 0.335}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 50 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 50, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3640149, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.641, "width_percent": 0.261}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.365259, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.902, "width_percent": 0.345}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.366602, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.247, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3677962, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.446, "width_percent": 0.303}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 52 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 52, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.369087, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.749, "width_percent": 0.251}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3702981, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 50, "width_percent": 0.345}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.371665, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.345, "width_percent": 0.376}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3731, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.721, "width_percent": 0.366}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 93 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 93, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.374372, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.087, "width_percent": 0.282}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.375675, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.369, "width_percent": 0.554}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.37737, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.923, "width_percent": 0.293}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.37875, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.216, "width_percent": 0.335}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.379191, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.551, "width_percent": 0.209}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.379493, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.76, "width_percent": 0.314}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3799338, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.073, "width_percent": 0.272}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38028, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.345, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380491, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.45, "width_percent": 0.188}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380716, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.638, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.380893, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.753, "width_percent": 0.251}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3812342, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.004, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3814478, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.108, "width_percent": 0.261}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3817532, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.37, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.381942, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.485, "width_percent": 0.314}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.382323, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.798, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.382549, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.892, "width_percent": 0.387}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.382986, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.279, "width_percent": 0.293}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3833308, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.572, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3836422, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.802, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3838549, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.906, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384084, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.105, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384247, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.22, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384446, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.345, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384623, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.429, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384833, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.607, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384978, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.711, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.385176, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.837, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.385356, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.92, "width_percent": 0.376}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3858101, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.297, "width_percent": 0.115}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386034, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.412, "width_percent": 0.178}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3862991, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.589, "width_percent": 0.314}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386736, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.903, "width_percent": 0.251}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.387058, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.154, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.387262, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.258, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3874822, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.436, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.387648, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.551, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3878622, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.687, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388068, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.781, "width_percent": 0.376}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388576, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.157, "width_percent": 0.282}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389203, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.44, "width_percent": 0.46}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389747, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.9, "width_percent": 0.167}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39003, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.067, "width_percent": 0.22}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390287, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.286, "width_percent": 0.366}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390694, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.652, "width_percent": 0.167}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3909302, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.82, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391113, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.914, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391343, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.112, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391495, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.227, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391752, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.363, "width_percent": 0.209}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392288, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.572, "width_percent": 0.439}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392774, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.011, "width_percent": 0.136}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39296, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.147, "width_percent": 0.146}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3931851, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.294, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3933809, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.388, "width_percent": 0.188}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393617, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.576, "width_percent": 0.366}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394044, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.942, "width_percent": 0.22}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394345, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.161, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394548, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.266, "width_percent": 0.188}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394809, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.454, "width_percent": 0.24}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39515, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.694, "width_percent": 0.209}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3954291, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.903, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3956301, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.997, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.395839, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.175, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3960152, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.301, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396241, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.437, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396605, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.667, "width_percent": 0.314}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396956, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.98, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397128, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.106, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397336, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.241, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397519, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.325, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397731, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.503, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397882, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.607, "width_percent": 0.24}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3981829, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.848, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.398363, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.931, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3985782, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.109, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.398725, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.214, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3989198, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.339, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399095, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.423, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399305, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.6, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399455, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.705, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3996449, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.83, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39982, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.914, "width_percent": 0.167}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4000242, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.081, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40017, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.186, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4003708, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.311, "width_percent": 0.272}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400761, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.583, "width_percent": 0.272}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4010782, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.855, "width_percent": 0.282}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401411, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.137, "width_percent": 0.157}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4016411, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.294, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401836, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.388, "width_percent": 0.178}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402092, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.566, "width_percent": 0.24}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402446, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.806, "width_percent": 0.209}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402724, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.015, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402927, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.12, "width_percent": 0.178}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403189, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.298, "width_percent": 0.22}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4035618, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.517, "width_percent": 0.272}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403889, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.789, "width_percent": 0.335}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4043102, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.123, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404801, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.406, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405181, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.667, "width_percent": 0.209}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4054291, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.876, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40559, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.991, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4058099, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.127, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405997, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.211, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4062161, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.388, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406366, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.493, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4065762, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.618, "width_percent": 0.293}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406982, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.911, "width_percent": 0.22}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407247, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.13, "width_percent": 0.282}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4075792, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.413, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407933, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.695, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408159, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.799, "width_percent": 0.345}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408602, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.144, "width_percent": 0.293}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4090252, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.437, "width_percent": 0.303}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409418, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.74, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409655, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.845, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410029, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.127, "width_percent": 0.178}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410341, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.305, "width_percent": 0.355}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410783, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.66, "width_percent": 0.314}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411234, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.974, "width_percent": 0.753}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412046, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.727, "width_percent": 0.157}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412302, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.883, "width_percent": 0.23}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412571, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.113, "width_percent": 0.23}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412847, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.343, "width_percent": 0.167}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4130912, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.511, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413289, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.605, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413526, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.803, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413676, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.908, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413889, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.044, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414062, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.127, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414271, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.305, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414438, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.41, "width_percent": 0.439}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414953, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.849, "width_percent": 0.115}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.415175, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.964, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4154072, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.162, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.415564, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.277, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.415773, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.413, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.415955, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.497, "width_percent": 0.188}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.416178, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.685, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.416327, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.789, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.416524, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.915, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.41671, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.999, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417006, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.239, "width_percent": 0.24}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417332, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.479, "width_percent": 0.366}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417788, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.845, "width_percent": 0.345}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418216, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.19, "width_percent": 0.209}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418458, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.399, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4186192, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.514, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418827, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.65, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419012, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.744, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419223, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.922, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4193711, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.027, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419558, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.152, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.41974, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.246, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4199471, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.424, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4201, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.539, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4203038, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.675, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420479, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.758, "width_percent": 0.188}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420707, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.946, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420855, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.051, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4210548, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.176, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421399, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.438, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421608, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.616, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421766, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.731, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4219568, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.856, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422134, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.94, "width_percent": 0.167}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422333, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.107, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422481, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.211, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4226768, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.337, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422854, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.42, "width_percent": 0.167}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4230502, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.588, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423202, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.692, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423383, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.807, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4235551, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.891, "width_percent": 0.167}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4237592, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.058, "width_percent": 0.094}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4238942, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.152, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4241, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.278, "width_percent": 0.261}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4245, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.539, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424778, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.779, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424956, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.905, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425174, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.041, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425369, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.135, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425578, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.313, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4257488, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.428, "width_percent": 0.251}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4260972, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.678, "width_percent": 0.146}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4263709, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.825, "width_percent": 0.209}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426624, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.034, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426788, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.149, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426996, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.285, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427196, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.389, "width_percent": 0.293}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4275508, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.682, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427733, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.766, "width_percent": 0.157}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4279659, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.922, "width_percent": 0.251}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4283369, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.173, "width_percent": 0.261}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428672, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.435, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428876, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.539, "width_percent": 0.178}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4291222, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.717, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429305, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.811, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429521, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.989, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429673, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.093, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429883, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.229, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430062, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.313, "width_percent": 0.167}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430302, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.48, "width_percent": 0.24}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430655, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.72, "width_percent": 0.209}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430953, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.93, "width_percent": 0.387}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431463, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.316, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4317741, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.546, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431967, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.64, "width_percent": 0.178}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432208, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.818, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432394, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.912, "width_percent": 0.167}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432594, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.079, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4327672, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.205, "width_percent": 0.136}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4329748, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.341, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433152, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.424, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433398, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.602, "width_percent": 0.24}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433764, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.843, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434027, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.041, "width_percent": 0.084}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434206, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.125, "width_percent": 0.167}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4344041, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.292, "width_percent": 0.105}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434551, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.397, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434741, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.522, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434933, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.616, "width_percent": 0.178}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43516, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.794, "width_percent": 0.345}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43556, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.139, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.435874, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.369, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.436092, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.473, "width_percent": 0.429}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4365568, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.902, "width_percent": 0.261}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.436858, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.163, "width_percent": 0.146}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4370801, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.31, "width_percent": 0.146}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437268, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.456, "width_percent": 0.136}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437444, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.592, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437637, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.717, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437839, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.811, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438083, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.01, "width_percent": 0.157}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438314, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.167, "width_percent": 0.45}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438839, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.616, "width_percent": 0.167}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.439112, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.784, "width_percent": 0.22}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.439374, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.003, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.439534, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.118, "width_percent": 0.146}, {"sql": "select distinct `tujuan` from `histori_perjalanan_kapal` where `kapal_id` = ? and `tujuan` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.439842, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.264, "width_percent": 0.335}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973485.599837, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.599, "width_percent": 1.035}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973485.601074, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.634, "width_percent": 0.857}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973485.602808, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.491, "width_percent": 0.585}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973485.603629, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.077, "width_percent": 0.512}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973485.604268, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.589, "width_percent": 0.575}, {"sql": "select * from `users` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973485.637609, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.164, "width_percent": 0.836}]}, "models": {"data": {"App\\Models\\ChecklistDataEngine": {"value": 273, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=1", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "?"}}, "App\\Models\\HistoriPerjalananKapal": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FHistoriPerjalananKapal.php&line=1", "ajax": false, "filename": "HistoriPerjalananKapal.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\JenisKapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FJenisKapal.php&line=1", "ajax": false, "filename": "JenisKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 294, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/checklist-engine/2NaDK50q4p\"\n]"}, "request": {"path_info": "/checklist-engine/2NaDK50q4p", "status_code": "<pre class=sf-dump id=sf-dump-264868535 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-264868535\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2057258415 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057258415\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-82670386 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-82670386\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2092598620 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InUrZHE5djlyVE5XbDd5cHVBdGJnYmc9PSIsInZhbHVlIjoiUSt2bkExdEw0OGxQMnFpbGw1K0hCU1VuR2RueUlEamRWdWI3dW56amV6MHEya09zQ3drL1dsVWcyTVYvbWJmVWc4MWRUTUJwamg3YlRvZnpydTZ6N0E1bWtueDNDd296OG42WWtBNmlsR0c3QUhaT2lBTHg4SWgxNCtGWHlaSzgiLCJtYWMiOiI1MDI5YTU4YzZmZWVjMzJhZjc0ZDU4YWMyMDY5MTA4ZmNmNDI4ZmNmNWI0NGZlYmY2MThmMjBjNmFlNmRhNTI1IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IkVGTExPMnhOWkMxSkRzelAwckNpWXc9PSIsInZhbHVlIjoiZjkzMXVaaTZHMEVVNU5jUjVvamlQYUs3a3B2bWZJVW15NGhxRWtmVGFYRXVlS3poYU9QT0ZYcWp2N0UxWno2a3ZGeDZ2ejlvUUdFdU4yVEpYV1U3Q1lwSGliOXVPY1NMbE42M2s3NWxORVBRSDZZYlp0eDZ4eXpBdDlyUFI4R2MiLCJtYWMiOiIxMTYyNDRhYjdlZDY3ZGY5OTUyNTQ1YTU3MmQ2ODA3ODhlNDBiOTAxMjVlMTRiZDQwMjQyYzY5NjY0NWJhYTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092598620\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2137001993 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137001993\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:04:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNUNklERzRldlc3dzRkL29pVS9xelE9PSIsInZhbHVlIjoib0syUmsySU5PR2R4TUV1R1hYbGRCS01SRnl0NXFqdmwxMXdNSWFhcmg4VTRaYVRUb3IrRDZiYlRPRFNxeE9YdGJFeko3Y1pEc2hWSmhHbXZhSTV4eGY0emZFeUczS0llQmNXU1ZMYnBJS3BkOXdaNHJUWGkyald1a000SCsyaE8iLCJtYWMiOiI4MTFjMTI3MDZlZThhNjliOTI1ZTZjOTc1MzQwZmU3NTQ3OGUzNmY1ZWI3OTcxNzg2ZGE1NzVhYTZjZDEyMjdjIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:04:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ino2b1k3WWdpekptK2dETmFEVjN2UUE9PSIsInZhbHVlIjoiOTZnenB5amxIa0RyREFOYTg0Mzc4R2pCbGI3TjhZZTk4b3RCZTcyT0hMODhVUHNTS3VPK0hCV2dnZW05K0hzeE9PbG52MUFCcjFHWEwxYnR0QmtUQjBCNk9mYk1ydlBPclNDWCtHOTJZZ3pXR3NyUUltN255K2NlUFY3dTFhdWoiLCJtYWMiOiJlNGJlNjY0NjZlZWQ0NjZiNGY4NDlkZWUzYjZhZjg4MTBmMDQ1ZjAyMWU4ZjI2ZDk3Mzg4YjU1MmMyMTJiMjE4IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:04:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNUNklERzRldlc3dzRkL29pVS9xelE9PSIsInZhbHVlIjoib0syUmsySU5PR2R4TUV1R1hYbGRCS01SRnl0NXFqdmwxMXdNSWFhcmg4VTRaYVRUb3IrRDZiYlRPRFNxeE9YdGJFeko3Y1pEc2hWSmhHbXZhSTV4eGY0emZFeUczS0llQmNXU1ZMYnBJS3BkOXdaNHJUWGkyald1a000SCsyaE8iLCJtYWMiOiI4MTFjMTI3MDZlZThhNjliOTI1ZTZjOTc1MzQwZmU3NTQ3OGUzNmY1ZWI3OTcxNzg2ZGE1NzVhYTZjZDEyMjdjIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:04:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ino2b1k3WWdpekptK2dETmFEVjN2UUE9PSIsInZhbHVlIjoiOTZnenB5amxIa0RyREFOYTg0Mzc4R2pCbGI3TjhZZTk4b3RCZTcyT0hMODhVUHNTS3VPK0hCV2dnZW05K0hzeE9PbG52MUFCcjFHWEwxYnR0QmtUQjBCNk9mYk1ydlBPclNDWCtHOTJZZ3pXR3NyUUltN255K2NlUFY3dTFhdWoiLCJtYWMiOiJlNGJlNjY0NjZlZWQ0NjZiNGY4NDlkZWUzYjZhZjg4MTBmMDQ1ZjAyMWU4ZjI2ZDk3Mzg4YjU1MmMyMTJiMjE4IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:04:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2068903018 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068903018\", {\"maxDepth\":0})</script>\n"}}