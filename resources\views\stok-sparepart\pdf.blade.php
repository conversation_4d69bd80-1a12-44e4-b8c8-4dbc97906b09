<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON>par<PERSON></title>
    <style>
        @page {
            margin: 1cm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 16px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-table {
            width: 100%;
            margin-bottom: 10px;
        }
        .info-table td {
            padding: 2px;
            font-size: 11px;
        }
        table.data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }
        .data-table th {
            background-color: #f0f0f0;
            padding: 5px 3px;
            text-align: center;
            font-weight: bold;
            border: 0.5px solid #000;
            font-size: 10px;
        }
        .data-table td {
            padding: 4px 3px;
            border: 0.5px solid #000;
            word-wrap: break-word;
        }
        .data-table th, .data-table td {
            font-size: 9px;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10px;
            position: fixed;
            bottom: 0;
            right: 0;
            padding: 10px;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .no-border {
            border: none !important;
        }
        .page-break {
            page-break-after: always;
        }
        .narrow-column {
            width: 5%;
        }
        .medium-column {
            width: 10%;
        }
        .wide-column {
            width: 15%;
        }
    </style>
</head>
<body>
    <div class="header">
        @if(isset($isAdminKapal) && $isAdminKapal)
            <h2>DATA SPAREPART DAN AMPRAHAN YANG ADA DI KAPAL</h2>
        @else
            <h2>LAPORAN STOK SPAREPART</h2>
        @endif
        <p>{{ $kapal->nama }}</p>
    </div>

    <table class="info-table">
        <tr>
            <td style="width: 100px">Periode</td>
            <td style="width: 10px">:</td>
            <td>{{ $tanggal_awal }} s/d {{ $tanggal_akhir }}</td>
        </tr>
        <tr>
            <td>Tanggal Cetak</td>
            <td>:</td>
            <td>{{ \Carbon\Carbon::now()->translatedFormat('d/m/Y H:i') }}</td>
        </tr>
    </table>

    <table class="data-table">
        <thead>
            <tr>
                <th class="narrow-column">No</th>
                <th class="medium-column">Tanggal</th>
                <th class="wide-column">Nama Barang</th>
                <th class="medium-column">Nomor Seri</th>
                <th class="medium-column">Jenis</th>
                <th class="narrow-column">Satuan</th>
                <th class="medium-column">Transaksi</th>
                <th class="narrow-column">Jumlah</th>
                <th class="narrow-column">Stok Sebelum</th>
                <th class="narrow-column">Stok Setelah</th>
                <th>Keterangan</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td class="text-center">{{ $item['tanggal'] }}</td>
                    <td>{{ $item['nama_barang'] }}</td>
                    <td>{{ $item['nomor_seri'] }}</td>
                    <td>{{ $item['jenis'] }}</td>
                    <td class="text-center">{{ $item['satuan'] }}</td>
                    <td>{{ $item['jenis_transaksi'] }}</td>
                    <td class="text-right">{{ $item['jumlah'] }}</td>
                    <td class="text-right">{{ $item['stok_sebelum'] }}</td>
                    <td class="text-right">{{ $item['stok_setelah'] }}</td>
                    <td>{{ $item['keterangan'] }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Dicetak oleh: {{ auth()->user()->name }}</p>
    </div>
</body>
</html> 