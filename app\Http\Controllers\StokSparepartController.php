<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\StokSparepartExport;
use App\Imports\StokSparepartImport;
use Barryvdh\DomPDF\Facade\Pdf;

class StokSparepartController extends Controller
{
    public function index()
    {
        // Ambil data stok sparepart yang sudah diterima kapal
        $stokSparepart = DB::table('data_sparepart as ds')
            ->select(
                'ds.id',
                'ds.nama_barang',
                'ds.nomor_seri',
                'ds.jenis',
                'ds.satuan',
                DB::raw('(
                    SELECT COALESCE(SUM(jumlah), 0) 
                    FROM riwayat_stok_kantor 
                    WHERE id_barang = ds.id 
                    AND jenis_transaksi = "penerimaan_sparepart_kapal"
                ) as total_diterima'),
                DB::raw('(
                    SELECT COALESCE(SUM(jumlah), 0) 
                    FROM riwayat_stok_kantor 
                    WHERE id_barang = ds.id 
                    AND jenis_transaksi = "pemakaian_sparepart_kapal"
                ) as total_pemakaian'),
                DB::raw('(
                    SELECT COALESCE(SUM(CASE 
                        WHEN jenis_transaksi = "penerimaan_sparepart_kapal" THEN jumlah 
                        WHEN jenis_transaksi = "pemakaian_sparepart_kapal" THEN -jumlah 
                        ELSE 0 
                    END), 0)
                    FROM riwayat_stok_kantor 
                    WHERE id_barang = ds.id 
                    AND jenis_transaksi IN ("penerimaan_sparepart_kapal", "pemakaian_sparepart_kapal")
                ) as stok_tersedia')
            )
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                      ->from('riwayat_stok_kantor')
                      ->whereRaw('riwayat_stok_kantor.id_barang = ds.id')
                      ->whereIn('jenis_transaksi', ['penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal']);
            })
            ->having('stok_tersedia', '>=', 0)
            ->get();

        // Ambil riwayat transaksi
        $riwayatTransaksi = DB::table('riwayat_stok_kantor as rsk')
            ->join('data_sparepart as ds', 'rsk.id_barang', '=', 'ds.id')
            ->whereIn('rsk.jenis_transaksi', ['penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal'])
            ->select(
                'rsk.created_at',
                'ds.nama_barang',
                'ds.nomor_seri',
                'ds.jenis',
                'rsk.jenis_transaksi',
                'rsk.jumlah',
                'rsk.stok_sebelum',
                'rsk.stok_setelah',
                'rsk.keterangan'
            )
            ->orderBy('rsk.created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($item) {
                return [
                    'tanggal' => Carbon::parse($item->created_at)->translatedFormat('l, d/m/Y'),
                    'nama_barang' => $item->nama_barang,
                    'nomor_seri' => $item->nomor_seri,
                    'jenis' => $item->jenis,
                    'jenis_transaksi' => $item->jenis_transaksi == 'penerimaan_sparepart_kapal' ? 'Penerimaan' : 'Pemakaian',
                    'jumlah' => $item->jumlah,
                    'stok_sebelum' => $item->stok_sebelum,
                    'stok_setelah' => $item->stok_setelah,
                    'keterangan' => $item->keterangan
                ];
            });

        return view('stok-sparepart.index', compact('stokSparepart', 'riwayatTransaksi'));
    }

    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            
            // Validasi input
            $request->validate([
                'id_barang' => 'required|exists:data_sparepart,id',
                'jumlah' => 'required|numeric|min:1',
                'keterangan' => 'nullable|string'
            ]);
            
            // Cek stok tersedia
            $stokTersedia = DB::table('penerimaan_kapal')
                ->where('id_barang', $request->id_barang)
                ->sum('jumlah') - 
                DB::table('pemakaian_kapal')
                ->where('id_barang', $request->id_barang)
                ->sum('jumlah');
                
            if ($request->jumlah > $stokTersedia) {
                throw new \Exception('Jumlah pemakaian melebihi stok yang tersedia');
            }
            
            // Catat pemakaian
            $pemakaian = DB::table('pemakaian_kapal')->insertGetId([
                'id_barang' => $request->id_barang,
                'jumlah' => $request->jumlah,
                'tanggal' => now(),
                'keterangan' => $request->keterangan,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // Ambil nama barang untuk keterangan
            $barang = DB::table('data_sparepart')
                ->where('id', $request->id_barang)
                ->first();
            
            // Catat di riwayat stok
            RiwayatStokKantor::create([
                'id_barang' => $request->id_barang,
                'jenis_transaksi' => 'pemakaian',
                'id_referensi' => $pemakaian,
                'jumlah' => $request->jumlah,
                'stok_sebelum' => $stokTersedia,
                'stok_setelah' => $stokTersedia - $request->jumlah,
                'keterangan' => "Pemakaian {$barang->nama_barang} oleh kapal" . 
                               ($request->keterangan ? " - {$request->keterangan}" : '')
            ]);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Pemakaian barang berhasil dicatat'
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    public function exportForm()
    {
        $kapals = DB::table('kapal')
                    ->select('id', 'nama', 'jenis_kapal_id as jenis')
                    ->get()
                    ->map(function($kapal) {
                        $kapal->jenis = $kapal->jenis == 1 ? 'Tugboat' : 'Barge';
                        return $kapal;
                    });

        return view('stok-sparepart.export-form', compact('kapals'));
    }

    public function export(Request $request)
    {
        $request->validate([
            'tanggal_awal' => 'required|date',
            'tanggal_akhir' => 'required|date|after_or_equal:tanggal_awal',
            'kapal_id' => 'required|exists:kapal,id',
            'type' => 'required|in:excel,pdf'
        ]);

        // Ambil nama kapal untuk nama file
        $kapal = DB::table('kapal')->where('id', $request->kapal_id)->first();

        // Format tanggal untuk nama file
        $startDate = Carbon::parse($request->tanggal_awal)->format('d-m-Y');
        $endDate = Carbon::parse($request->tanggal_akhir)->format('d-m-Y');

        // Cek apakah user adalah admin_kapal
        $isAdminKapal = auth()->user()->isAdminKapal();

        if ($request->type === 'excel') {
            // Tentukan nama file berdasarkan role
            if ($isAdminKapal) {
                $fileName = 'Data Sparepart dan Amprahan yang Ada di Kapal ' . $kapal->nama . ' (' . $startDate . ' sd ' . $endDate . ').xlsx';
            } else {
                $fileName = 'Laporan Stok Sparepart ' . $kapal->nama . ' (' . $startDate . ' sd ' . $endDate . ').xlsx';
            }

            $export = new StokSparepartExport(
                $request->tanggal_awal,
                $request->tanggal_akhir,
                $request->kapal_id,
                $isAdminKapal
            );

            return Excel::download($export, $fileName);
        } else {
            // Untuk PDF, ambil data sesuai role
            if ($isAdminKapal) {
                // Hanya data kapal untuk admin_kapal
                $data = DB::table('riwayat_stok_kantor as rsk')
                    ->join('data_sparepart as ds', 'rsk.id_barang', '=', 'ds.id')
                    ->whereIn('rsk.jenis_transaksi', ['penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal'])
                    ->where('rsk.kapal_id', $request->kapal_id)
                    ->whereBetween('rsk.created_at', [
                        Carbon::parse($request->tanggal_awal)->startOfDay(),
                        Carbon::parse($request->tanggal_akhir)->endOfDay()
                    ])
                    ->select(
                        'rsk.created_at as tanggal',
                        'ds.nama_barang',
                        'ds.nomor_seri',
                        'ds.jenis',
                        'ds.satuan',
                        'rsk.jenis_transaksi',
                        'rsk.jumlah',
                        'rsk.stok_sebelum',
                        'rsk.stok_setelah',
                        'rsk.keterangan'
                    )
                    ->orderBy('rsk.created_at', 'desc')
                    ->get()
                    ->map(function($item) {
                        return [
                            'tanggal' => Carbon::parse($item->tanggal)->translatedFormat('d/m/Y'),
                            'nama_barang' => $item->nama_barang,
                            'nomor_seri' => $item->nomor_seri,
                            'jenis' => $item->jenis,
                            'satuan' => $item->satuan,
                            'jenis_transaksi' => $item->jenis_transaksi == 'penerimaan_sparepart_kapal' ? 'Penerimaan' : 'Pemakaian',
                            'jumlah' => $item->jumlah,
                            'stok_sebelum' => $item->stok_sebelum,
                            'stok_setelah' => $item->stok_setelah,
                            'keterangan' => $item->keterangan
                        ];
                    });

                $fileName = 'Data Sparepart dan Amprahan yang Ada di Kapal ' . $kapal->nama . ' (' . $startDate . ' sd ' . $endDate . ').pdf';
            } else {
                // Data lengkap untuk role lain
                $data = DB::table('riwayat_stok_kantor as rsk')
                    ->join('data_sparepart as ds', 'rsk.id_barang', '=', 'ds.id')
                    ->whereIn('rsk.jenis_transaksi', ['penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal'])
                    ->whereBetween('rsk.created_at', [
                        Carbon::parse($request->tanggal_awal)->startOfDay(),
                        Carbon::parse($request->tanggal_akhir)->endOfDay()
                    ])
                    ->select(
                        'rsk.created_at as tanggal',
                        'ds.nama_barang',
                        'ds.nomor_seri',
                        'ds.jenis',
                        'ds.satuan',
                        'rsk.jenis_transaksi',
                        'rsk.jumlah',
                        'rsk.stok_sebelum',
                        'rsk.stok_setelah',
                        'rsk.keterangan'
                    )
                    ->orderBy('rsk.created_at', 'desc')
                    ->get()
                    ->map(function($item) {
                        return [
                            'tanggal' => Carbon::parse($item->tanggal)->translatedFormat('d/m/Y'),
                            'nama_barang' => $item->nama_barang,
                            'nomor_seri' => $item->nomor_seri,
                            'jenis' => $item->jenis,
                            'satuan' => $item->satuan,
                            'jenis_transaksi' => $item->jenis_transaksi == 'penerimaan_sparepart_kapal' ? 'Penerimaan' : 'Pemakaian',
                            'jumlah' => $item->jumlah,
                            'stok_sebelum' => $item->stok_sebelum,
                            'stok_setelah' => $item->stok_setelah,
                            'keterangan' => $item->keterangan
                        ];
                    });

                $fileName = 'Laporan Stok Sparepart ' . $kapal->nama . ' (' . $startDate . ' sd ' . $endDate . ').pdf';
            }

            $pdf = PDF::loadView('stok-sparepart.pdf', [
                'data' => $data,
                'kapal' => $kapal,
                'tanggal_awal' => $startDate,
                'tanggal_akhir' => $endDate,
                'isAdminKapal' => $isAdminKapal
            ]);

            return $pdf->download($fileName);
        }
    }

    public function import(Request $request) 
    {
        $request->validate([
            'file' => 'required|mimes:csv,txt',
            'kapal_id' => 'required|exists:kapal,id',
            'tanggal' => 'required|date'
        ]);

        try {
            Excel::import(new StokSparepartImport($request->kapal_id, $request->tanggal), $request->file('file'));
            
            return redirect()->back()->with('success', 'Data berhasil diimport');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function importForm()
    {
        $kapalList = \App\Models\Kapal::all();
        return view('stok-sparepart.import-form', compact('kapalList'));
    }
} 