{"__meta": {"id": "X7f85614ac4eb47a954cad777d987cbe4", "datetime": "2025-07-20 07:26:10", "utime": 1752971170.240582, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752971168.956836, "end": 1752971170.240611, "duration": 1.2837750911712646, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1752971168.956836, "relative_start": 0, "end": **********.142288, "relative_end": **********.142288, "duration": 0.18545198440551758, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.142301, "relative_start": 0.18546509742736816, "end": 1752971170.240615, "relative_end": 3.814697265625e-06, "duration": 1.098313808441162, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25695032, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=16\" onclick=\"\">app/Http/Controllers/Auth/LoginController.php:16-35</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01256, "accumulated_duration_str": "12.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 167}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 127}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 23}], "start": **********.813144, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:167", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=167", "ajax": false, "filename": "EloquentUserProvider.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `username` = 'admin_kapal' limit 1", "type": "query", "params": [], "bindings": ["admin_kapal"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8179152, "duration": 0.01256, "duration_str": "12.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1583060749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1583060749\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2018066513 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DvzKpXf3twh86OzP5SEv5t8FIgpl7SSI8971pMp2</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"11 characters\">admin_kapal</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2018066513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-573075217 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">86</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklBbmt0ZDZwK09GNEJsN1F1aXl6SEE9PSIsInZhbHVlIjoiVVhtWndmQ2x2eTYreWlROGk5S0xFUUcvNDFmU0hKVWw1OCtvMXJqcjBJWlFYWVhSZ3hPQUpVcThDN0RyRHl3ellaYlI0M25DclRVUFR2T3pSYzdaekNxQ0lUN3NTSm1MT05Obkx1bmFZd0xYa2MyU1lXelhZMUM5NXlXUTlqdWkiLCJtYWMiOiJmYmMwMjZkNGU2ODNkMjAzY2I5YTA4YmJmZmJmODVhMmE5NTcwMmJmMTczMjczMjQwYzU1YWM3ZTU0YWM5OTU3IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6Ii9SLzRzVzNtR3N0eE1PcFhnK0dBUEE9PSIsInZhbHVlIjoib3c4eFRVcnVReTE0cXlVZEVoU3BtZmhUTTFCTTJMVGExa3krOUlTVEdKc3VFaDEvU3RJdDB5M2VYVURDK21Pd0FoK2lhWEd0L08xNkpvRFkzN2N2YXFWY3VKalNiK0hmT291R1B0MXNVL29DQWVqUTA1S244ZjNza0FySEloQkUiLCJtYWMiOiI0ZjRmMTY3ZjdmNzVmMzQ0NmM2M2Q3NDgzZDRlZTU2YmE5ZTNkYzNiZjIxNDcwYjRhNzZjZWMyM2U5OTk3ZTk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573075217\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-333370220 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DvzKpXf3twh86OzP5SEv5t8FIgpl7SSI8971pMp2</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">97Gi97yAdnkTpDKZvrHQIw6tprT8eNMRRLCYndVm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333370220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1345797882 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:26:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://sikapal.test/stok-sparepart/export-form</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ilg4MWJIVXljOFQ5czRFWkR5blVCc2c9PSIsInZhbHVlIjoiUFZKSlBLSWJZeG1ubXBQeElQSmU5a08yTDNOc1YzblhHYUR4T2c4NDR0Tjc5akpUZk5oVlV4UjhVQ2RHVmJCYkxZeXlvYmhCRk1TTVVwVEw4RGkzOXFoUUdjVTdpOVFDTDRpS2JiZ24ycWRnK2creG1Sa0lzU2JoVkI2WSt0T0wiLCJtYWMiOiIwOGViOGRkMWEzOGZlYTdlMWYyYmEzMTAzMmFjOTUxNThjMWEzNzY5Mjc1MmNiNTY2ODBlODVjNTJmZDQ2MTI4IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:26:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Im92UE81NG83alJWdVowRG8weEg5eUE9PSIsInZhbHVlIjoiYWpOYlNwYWxzenhtRWloeEJsaTNSZHZrdmx1QWIxTnBGdEFFOGZsdDNQWWhBTXJWbGpZdmlrekxZcjZVcG85SGNIc1N4SWw4NWI0ei91QWZhbWtjYmhiditRRk9QQXNmTjdKQjArT3d0eUczaTZ5cVl0Ky9SZGJncldHYjBJY0YiLCJtYWMiOiIyNDljOTgxZGI2NjAxZTg4YjNkOTIzNGZkNDQ4NjkzYzYwZmM0NzU4YzYxOGRlY2JlNDA5OTQyOGViNjVkYmU1IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:26:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ilg4MWJIVXljOFQ5czRFWkR5blVCc2c9PSIsInZhbHVlIjoiUFZKSlBLSWJZeG1ubXBQeElQSmU5a08yTDNOc1YzblhHYUR4T2c4NDR0Tjc5akpUZk5oVlV4UjhVQ2RHVmJCYkxZeXlvYmhCRk1TTVVwVEw4RGkzOXFoUUdjVTdpOVFDTDRpS2JiZ24ycWRnK2creG1Sa0lzU2JoVkI2WSt0T0wiLCJtYWMiOiIwOGViOGRkMWEzOGZlYTdlMWYyYmEzMTAzMmFjOTUxNThjMWEzNzY5Mjc1MmNiNTY2ODBlODVjNTJmZDQ2MTI4IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:26:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Im92UE81NG83alJWdVowRG8weEg5eUE9PSIsInZhbHVlIjoiYWpOYlNwYWxzenhtRWloeEJsaTNSZHZrdmx1QWIxTnBGdEFFOGZsdDNQWWhBTXJWbGpZdmlrekxZcjZVcG85SGNIc1N4SWw4NWI0ei91QWZhbWtjYmhiditRRk9QQXNmTjdKQjArT3d0eUczaTZ5cVl0Ky9SZGJncldHYjBJY0YiLCJtYWMiOiIyNDljOTgxZGI2NjAxZTg4YjNkOTIzNGZkNDQ4NjkzYzYwZmM0NzU4YzYxOGRlY2JlNDA5OTQyOGViNjVkYmU1IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:26:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345797882\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}