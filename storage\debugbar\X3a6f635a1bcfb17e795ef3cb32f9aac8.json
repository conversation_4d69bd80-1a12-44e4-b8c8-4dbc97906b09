{"__meta": {"id": "X3a6f635a1bcfb17e795ef3cb32f9aac8", "datetime": "2025-07-20 07:16:44", "utime": **********.915592, "method": "GET", "uri": "/admin/kantor/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[07:16:44] LOG.info: Stok Kapal Kosong: {\n    \"query\": [],\n    \"result\": [\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 225,\n            \"nama_barang\": \"FUEL FILTER SCREEN\",\n            \"nomor_seri\": \"FUEL FILTER SCREEN\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 226,\n            \"nama_barang\": \"PRESSURE TRANSMITER\",\n            \"nomor_seri\": \"04541 - 90200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 227,\n            \"nama_barang\": \"GASKET CYL HEAD (TSP)\",\n            \"nomor_seri\": \"3750112200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 228,\n            \"nama_barang\": \"PACKING, ROCKER CASE\",\n            \"nomor_seri\": \"3750441200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 229,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"3750402300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 230,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550710200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 231,\n            \"nama_barang\": \"SEAL, VALVE STEM\",\n            \"nomor_seri\": \"3750400900\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 232,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550531065\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 233,\n            \"nama_barang\": \"ORING LINER\",\n            \"nomor_seri\": \"3750732400\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 234,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704201\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 235,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.891921, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.613846, "end": **********.915618, "duration": 0.301771879196167, "duration_str": "302ms", "measures": [{"label": "Booting", "start": **********.613846, "relative_start": 0, "end": **********.796089, "relative_end": **********.796089, "duration": 0.18224287033081055, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.796104, "relative_start": 0.18225789070129395, "end": **********.915621, "relative_end": 3.0994415283203125e-06, "duration": 0.11951708793640137, "duration_str": "120ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25710304, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "admin_kantor.dashboard", "param_count": null, "params": [], "start": **********.904568, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/dashboard.blade.phpadmin_kantor.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.908785, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.910083, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.911568, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/kantor/dashboard", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\AdminKantorDashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "admin.kantor.dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=12\" onclick=\"\">app/Http/Controllers/AdminKantorDashboardController.php:12-168</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.032060000000000005, "accumulated_duration_str": "32.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.835053, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.8390129, "duration": 0.0206, "duration_str": "20.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 64.255}, {"sql": "select count(*) as aggregate from `pembelian_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.864103, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:15", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=15", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "15"}, "connection": "gema_kapal", "explain": null, "start_percent": 64.255, "width_percent": 7.424}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.867856, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:18", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=18", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "18"}, "connection": "gema_kapal", "explain": null, "start_percent": 71.678, "width_percent": 5.833}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.870912, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:22", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=22", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "22"}, "connection": "gema_kapal", "explain": null, "start_percent": 77.511, "width_percent": 1.56}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8725169, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:23", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=23", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 79.07, "width_percent": 1.778}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.874003, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=29", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "29"}, "connection": "gema_kapal", "explain": null, "start_percent": 80.848, "width_percent": 1.279}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.875288, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=35", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "35"}, "connection": "gema_kapal", "explain": null, "start_percent": 82.127, "width_percent": 0.437}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.876332, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:41", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=41", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "41"}, "connection": "gema_kapal", "explain": null, "start_percent": 82.564, "width_percent": 0.998}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.877528, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=42", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "42"}, "connection": "gema_kapal", "explain": null, "start_percent": 83.562, "width_percent": 0.405}, {"sql": "select count(*) as aggregate from `data_sparepart`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.878692, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:48", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=48", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "48"}, "connection": "gema_kapal", "explain": null, "start_percent": 83.968, "width_percent": 5.022}, {"sql": "select count(*) as aggregate from `data_sparepart` where month(`created_at`) = '06'", "type": "query", "params": [], "bindings": ["06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8813589, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:53", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=53", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "53"}, "connection": "gema_kapal", "explain": null, "start_percent": 88.989, "width_percent": 0.717}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where `created_at` >= '2025-07-06 07:16:44'", "type": "query", "params": [], "bindings": ["2025-07-06 07:16:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.882576, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:60", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=60", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "60"}, "connection": "gema_kapal", "explain": null, "start_percent": 89.707, "width_percent": 1.123}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where `created_at` >= '2025-07-06 07:16:44'", "type": "query", "params": [], "bindings": ["2025-07-06 07:16:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.883926, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:63", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=63", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "63"}, "connection": "gema_kapal", "explain": null, "start_percent": 90.83, "width_percent": 0.53}, {"sql": "select `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, (\nSELECT COALESCE(SUM(CASE\nWHEN jenis_transaksi = \"penerimaan_sparepart_kapal\" THEN jumlah\nWHEN jenis_transaksi = \"pemakaian_sparepart_kapal\" THEN -jumlah\nELSE 0\nEND), 0)\nFROM riwayat_stok_kantor\nWHERE id_barang = ds.id\nAND jenis_transaksi IN (\"penerimaan_sparepart_kapal\", \"pemakaian_sparepart_kapal\")\n) as stok_tersedia, (\nSELECT GROUP_CONCAT(DISTINCT pk.kapal_id)\nFROM pengeluaran_kantor pk\nJOIN pembelian_kantor pmk ON pmk.id = pk.id_pembelian\nWHERE pmk.id_barang = ds.id\nAND pk.jumlah > 0\n) as kapal_dengan_stok from `data_sparepart` as `ds` where exists (select 1 from `riwayat_stok_kantor` where riwayat_stok_kantor.id_barang = ds.id and `jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal')) having `stok_tersedia` <= 0 and `kapal_dengan_stok` = null", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", 0, null], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 99}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.885404, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:99", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=99", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 91.36, "width_percent": 5.958}, {"sql": "select `k`.`id` as `kapal_id`, `k`.`nama` as `nama_kapal`, `k`.`jenis_kapal_id`, `ds`.`id` as `id_barang`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, COALESCE(SUM(prk.jumlah), 0) as stok_tersedia, MAX(pk.jumlah) as jumlah_pengeluaran from `kapal` as `k` inner join `pengeluaran_kantor` as `pk` on `k`.`id` = `pk`.`kapal_id` inner join `pembelian_kantor` as `pmk` on `pmk`.`id` = `pk`.`id_pembelian` inner join `data_sparepart` as `ds` on `pmk`.`id_barang` = `ds`.`id` left join `penerimaan_kapal` as `prk` on `k`.`id` = `prk`.`kapal_id` and `pk`.`id` = `prk`.`pengeluaran_kantor_id` where exists (select 1 from `pengeluaran_kantor` inner join `pembelian_kantor` on `pembelian_kantor`.`id` = `pengeluaran_kantor`.`id_pembelian` where `pengeluaran_kantor`.`kapal_id` = `k`.`id` and `pembelian_kantor`.`id_barang` = `ds`.`id`) group by `k`.`id`, `k`.`nama`, `k`.`jenis_kapal_id`, `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan` having `stok_tersedia` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 142}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.888704, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:142", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 142}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=142", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "142"}, "connection": "gema_kapal", "explain": null, "start_percent": 97.318, "width_percent": 2.682}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/admin/kantor/dashboard\"\n]"}, "request": {"path_info": "/admin/kantor/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-787634407 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-787634407\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-566106380 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-566106380\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-589158589 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-589158589\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1843232167 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://sikapal.test/riwayat-pembelian?page=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImpKSzVwNm5TbUFsS0V0QkkraVBzNXc9PSIsInZhbHVlIjoiTGc0MmgzV1lyZ1VrcmdTZktqTDJuREd1MkQ1TUFrRHF3d2tGU2lXTGNnWHErbm5pR1JsZWgzU0JjSWtoMUU2aGo5TjBUZGw2dmYvWVhwaE9GTHpXTjcyWVFuWkpsS2xmOHlFNjV4bCtRUEswMndiWTMxS3JxN0g2UWozOU1Ya1EiLCJtYWMiOiI5MGYyMzFhODBlNzJkMGVmOTg2MjYzMzZmNTU2Njg2MDYyMGVmYzRjYmZlYWY3N2ZhZDVhYmY5ZGMxYTI0YTc4IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IjR4T1NEZU5yTHNBTTlNYzZhNld4cVE9PSIsInZhbHVlIjoiQVE0RkdGN2RRaVFGMVl0TEVxN1ZlR0hwNnB2OGtpNWRDQXQwbnJRcy9sZUpraVJiOU9SanFkTkhKcTM0NmNkMTV6eis5NTF0UnJOTTdXOFBwa2t2cE9hcWlQQW9zVnBEeGJZQXVJQ1N6dnMyUTZ6UU1ZMnc4b2R2T0hOTzVUdGsiLCJtYWMiOiJjZjM2MTVmMTc4ODYzZmYzMzk1ZTJhOTgzNTI0ZTY4ZmE2MDA0NThjN2U3ZmZhZjU5NDYyZWRiYTJhZTBiNzZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1843232167\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-746988996 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-746988996\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:16:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImVNZTlwdVVYVkJKaThwTXdVblppMHc9PSIsInZhbHVlIjoiYWVtQTlxQUhwY3ZNYmhoUHVoalAzMyt4Q0Q0MmNnWXRUZW5LendJQTZsMjg2QmhXdmpKNlJFdFFKM3h3Q0J0RG5TU0tnWTlXWW9FWHUxYTFzVGpONG1aSXhUN2RIU2dSbUtURHJSd1R3LzBjZHczVzJybmhKMVRYRVVFemYyTTkiLCJtYWMiOiI1OWUzYzI2NmUxODBjNDc2MzZhMGMxODA5ZmYxODdlM2I1OWJkNjQ5Y2U0ZTAxYzI1ZGEzYzQ4ZTJhNmEzYTRmIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:16:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IjN2K2pOQkpCbENGcCtWRFlxaGtNT3c9PSIsInZhbHVlIjoiK2lJTXpVL3crTTlZdlBRRGJ5anhXYlZTQldkRlMzSmJpQWw5cnJnelIxOVVZcDlpWjlOZHZBS205Y2Y1T1BuMHJNeU5zU3FmcmtLaG5qempleVdiR2tvRVVBc29wcTBKOERHZjVDZkVicy8vQmZVNG1ocmdnS2oxakdzalpxOG4iLCJtYWMiOiI5Y2M5NzgyOTNjY2MzYTk2OTBlOGQ5N2QzZmQ4YTAzOTM2OGViMDRmZDM1N2RiZGU0YmI2NTNkOGNmYjEyOTFhIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:16:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImVNZTlwdVVYVkJKaThwTXdVblppMHc9PSIsInZhbHVlIjoiYWVtQTlxQUhwY3ZNYmhoUHVoalAzMyt4Q0Q0MmNnWXRUZW5LendJQTZsMjg2QmhXdmpKNlJFdFFKM3h3Q0J0RG5TU0tnWTlXWW9FWHUxYTFzVGpONG1aSXhUN2RIU2dSbUtURHJSd1R3LzBjZHczVzJybmhKMVRYRVVFemYyTTkiLCJtYWMiOiI1OWUzYzI2NmUxODBjNDc2MzZhMGMxODA5ZmYxODdlM2I1OWJkNjQ5Y2U0ZTAxYzI1ZGEzYzQ4ZTJhNmEzYTRmIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:16:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IjN2K2pOQkpCbENGcCtWRFlxaGtNT3c9PSIsInZhbHVlIjoiK2lJTXpVL3crTTlZdlBRRGJ5anhXYlZTQldkRlMzSmJpQWw5cnJnelIxOVVZcDlpWjlOZHZBS205Y2Y1T1BuMHJNeU5zU3FmcmtLaG5qempleVdiR2tvRVVBc29wcTBKOERHZjVDZkVicy8vQmZVNG1ocmdnS2oxakdzalpxOG4iLCJtYWMiOiI5Y2M5NzgyOTNjY2MzYTk2OTBlOGQ5N2QzZmQ4YTAzOTM2OGViMDRmZDM1N2RiZGU0YmI2NTNkOGNmYjEyOTFhIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:16:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1893376659 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893376659\", {\"maxDepth\":0})</script>\n"}}