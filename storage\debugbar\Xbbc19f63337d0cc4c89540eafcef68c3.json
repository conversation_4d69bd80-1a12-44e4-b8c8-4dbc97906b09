{"__meta": {"id": "Xbbc19f63337d0cc4c89540eafcef68c3", "datetime": "2025-07-20 07:15:26", "utime": 1752970526.005135, "method": "GET", "uri": "/admin/kantor/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[07:15:25] LOG.info: Stok Kapal Kosong: {\n    \"query\": [],\n    \"result\": [\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 225,\n            \"nama_barang\": \"FUEL FILTER SCREEN\",\n            \"nomor_seri\": \"FUEL FILTER SCREEN\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 226,\n            \"nama_barang\": \"PRESSURE TRANSMITER\",\n            \"nomor_seri\": \"04541 - 90200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 227,\n            \"nama_barang\": \"GASKET CYL HEAD (TSP)\",\n            \"nomor_seri\": \"3750112200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 228,\n            \"nama_barang\": \"PACKING, ROCKER CASE\",\n            \"nomor_seri\": \"3750441200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 229,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"3750402300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 230,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550710200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 231,\n            \"nama_barang\": \"SEAL, VALVE STEM\",\n            \"nomor_seri\": \"3750400900\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 232,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550531065\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 233,\n            \"nama_barang\": \"ORING LINER\",\n            \"nomor_seri\": \"3750732400\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 234,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704201\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 235,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.95289, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.412681, "end": 1752970526.005159, "duration": 0.5924777984619141, "duration_str": "592ms", "measures": [{"label": "Booting", "start": **********.412681, "relative_start": 0, "end": **********.619455, "relative_end": **********.619455, "duration": 0.20677399635314941, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.619476, "relative_start": 0.20679497718811035, "end": 1752970526.005162, "relative_end": 3.0994415283203125e-06, "duration": 0.38568592071533203, "duration_str": "386ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25710736, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "admin_kantor.dashboard", "param_count": null, "params": [], "start": **********.966985, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/dashboard.blade.phpadmin_kantor.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.971305, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.972585, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.974443, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/kantor/dashboard", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\AdminKantorDashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "admin.kantor.dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=12\" onclick=\"\">app/Http/Controllers/AdminKantorDashboardController.php:12-168</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.054599999999999996, "accumulated_duration_str": "54.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.749116, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.755042, "duration": 0.02028, "duration_str": "20.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 37.143}, {"sql": "select count(*) as aggregate from `pembelian_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.795609, "duration": 0.00784, "duration_str": "7.84ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:15", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=15", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "15"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.143, "width_percent": 14.359}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.814922, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:18", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=18", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "18"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.502, "width_percent": 4.377}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.818467, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:22", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=22", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "22"}, "connection": "gema_kapal", "explain": null, "start_percent": 55.879, "width_percent": 2.344}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.820938, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:23", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=23", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 58.223, "width_percent": 0.842}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.822441, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=29", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "29"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.066, "width_percent": 0.897}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8239841, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=35", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "35"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.963, "width_percent": 0.531}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8256848, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:41", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=41", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "41"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.495, "width_percent": 1.117}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.827493, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=42", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "42"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.612, "width_percent": 0.604}, {"sql": "select count(*) as aggregate from `data_sparepart`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.82901, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:48", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=48", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "48"}, "connection": "gema_kapal", "explain": null, "start_percent": 62.216, "width_percent": 7.747}, {"sql": "select count(*) as aggregate from `data_sparepart` where month(`created_at`) = '06'", "type": "query", "params": [], "bindings": ["06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.834477, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:53", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=53", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "53"}, "connection": "gema_kapal", "explain": null, "start_percent": 69.963, "width_percent": 1.3}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where `created_at` >= '2025-07-06 07:15:25'", "type": "query", "params": [], "bindings": ["2025-07-06 07:15:25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.836298, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:60", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=60", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "60"}, "connection": "gema_kapal", "explain": null, "start_percent": 71.264, "width_percent": 1.044}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where `created_at` >= '2025-07-06 07:15:25'", "type": "query", "params": [], "bindings": ["2025-07-06 07:15:25"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.837873, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:63", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=63", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "63"}, "connection": "gema_kapal", "explain": null, "start_percent": 72.308, "width_percent": 1.099}, {"sql": "select `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, (\nSELECT COALESCE(SUM(CASE\nWHEN jenis_transaksi = \"penerimaan_sparepart_kapal\" THEN jumlah\nWHEN jenis_transaksi = \"pemakaian_sparepart_kapal\" THEN -jumlah\nELSE 0\nEND), 0)\nFROM riwayat_stok_kantor\nWHERE id_barang = ds.id\nAND jenis_transaksi IN (\"penerimaan_sparepart_kapal\", \"pemakaian_sparepart_kapal\")\n) as stok_tersedia, (\nSELECT GROUP_CONCAT(DISTINCT pk.kapal_id)\nFROM pengeluaran_kantor pk\nJOIN pembelian_kantor pmk ON pmk.id = pk.id_pembelian\nWHERE pmk.id_barang = ds.id\nAND pk.jumlah > 0\n) as kapal_dengan_stok from `data_sparepart` as `ds` where exists (select 1 from `riwayat_stok_kantor` where riwayat_stok_kantor.id_barang = ds.id and `jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal')) having `stok_tersedia` <= 0 and `kapal_dengan_stok` = null", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", 0, null], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 99}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.860287, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:99", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=99", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 73.407, "width_percent": 10.714}, {"sql": "select `k`.`id` as `kapal_id`, `k`.`nama` as `nama_kapal`, `k`.`jenis_kapal_id`, `ds`.`id` as `id_barang`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, COALESCE(SUM(prk.jumlah), 0) as stok_tersedia, MAX(pk.jumlah) as jumlah_pengeluaran from `kapal` as `k` inner join `pengeluaran_kantor` as `pk` on `k`.`id` = `pk`.`kapal_id` inner join `pembelian_kantor` as `pmk` on `pmk`.`id` = `pk`.`id_pembelian` inner join `data_sparepart` as `ds` on `pmk`.`id_barang` = `ds`.`id` left join `penerimaan_kapal` as `prk` on `k`.`id` = `prk`.`kapal_id` and `pk`.`id` = `prk`.`pengeluaran_kantor_id` where exists (select 1 from `pengeluaran_kantor` inner join `pembelian_kantor` on `pembelian_kantor`.`id` = `pengeluaran_kantor`.`id_pembelian` where `pengeluaran_kantor`.`kapal_id` = `k`.`id` and `pembelian_kantor`.`id_barang` = `ds`.`id`) group by `k`.`id`, `k`.`nama`, `k`.`jenis_kapal_id`, `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan` having `stok_tersedia` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 142}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8798208, "duration": 0.00867, "duration_str": "8.67ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:142", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 142}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=142", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "142"}, "connection": "gema_kapal", "explain": null, "start_percent": 84.121, "width_percent": 15.879}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/admin/kantor/dashboard\"\n]"}, "request": {"path_info": "/admin/kantor/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-930671143 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-930671143\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2013223657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2013223657\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1923493584 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1923493584\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-311210608 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InBVeEE0eU0xVm92WDllZ3cweW03OVE9PSIsInZhbHVlIjoiMlpneXNQbUhtSHVWMXNsNE9DU2phS3AvYUN2eWlGVHhsWmpySnpqbDFqY1l0TG1QUENzQi9jdWtZc05FTFRIWHQxbnBIYUdYL0VybE1SdHNDTjRYNEt4Tk11NkZUdzlZZ1dHVVI1VjNNK2pXMEJTUVpYeUkwQjJMOVVQUVZualUiLCJtYWMiOiIwMjg1NjdkOTBjYzgxOTMwNWQ0MDlmM2Y0ODAzMzMzM2UzZmYwMGE2NWY0M2E2ZTgyYTdjY2ZmMzA2ZmY2NjJhIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IktXUHlVeG9MbWxQb0lLVTJFdE1na1E9PSIsInZhbHVlIjoidlVhZ0svWnNiMU14cU1mdVJNMFJJT3p5YXd1WWtNQnJLTCtOSGpsd1l2ZmtYTitKTlc1elZNcUh0VUh4RFlOOWFnczFidFQ5UVJSNFZkTWpmWTd4YW96VWo4UElpK0k2WFYxYWpPa1pvNWl5YnlhOFZPQ21aQjNZYjNka2twWXEiLCJtYWMiOiJjN2RlNDFkM2NkZWY3ZTI4MDU5ZDA1NWYzYzY3MmExOTZiNmEyYTQ0MDJlM2NmZTkyMGU0ZTExZDVlNjYxZmViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311210608\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1265403342 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1265403342\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1460424329 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:15:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IllWNnl0N3RrWnV2ZjM0UTRucWErTnc9PSIsInZhbHVlIjoib0lVQ3JxSkZKbGgvR2pKRnM4eEM2dVFoK0F2VEhLOCtpVTM0TllHRTJMVCtWcmpwTGh0bUlJdjNzbHZqdGlKNjVJYUNyN2EvOU9GUzc0UFlQYzM4b2JIV2V5N3N1Y051c3JtZlNkSEJ0N2I4SWt2b2hqM2lsRzNBa3lMTkp4SWEiLCJtYWMiOiI5MTk1OTkxZDIxYjYxNjBmMmIxZDY5NjBlMWI5NjA5NGQ3M2VkNDNlZTgxODM4YWQ0ZDgzZTEzZmY0NjJkMDAwIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImQ5SUUxN3RqZ3pvdmY5dVlTVzlGYkE9PSIsInZhbHVlIjoiOVRmWE9NMThLNGpKc01KOW9IZTFkWjVvMnpJbTVUdGduVXU3TGVGNFlaQ041NUQ2N1h5a1NHdHIvckRINlpiN2MzS2daL2lZVStMd2QvU01la0REV1p3QnZJU0UvdWlrVHBUbFlkS1lhaW0wMzRVcFBjakNpMXluVFQzVmlhcVciLCJtYWMiOiI2ZjZjNTIyMjBhZDQ0Mzk4OGQ3M2IyMWU3OWY4YTQxYzZkN2UxODQ3ZDVmZTFhZGUyNGFmMzcwYjA3OTg1MDM1IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IllWNnl0N3RrWnV2ZjM0UTRucWErTnc9PSIsInZhbHVlIjoib0lVQ3JxSkZKbGgvR2pKRnM4eEM2dVFoK0F2VEhLOCtpVTM0TllHRTJMVCtWcmpwTGh0bUlJdjNzbHZqdGlKNjVJYUNyN2EvOU9GUzc0UFlQYzM4b2JIV2V5N3N1Y051c3JtZlNkSEJ0N2I4SWt2b2hqM2lsRzNBa3lMTkp4SWEiLCJtYWMiOiI5MTk1OTkxZDIxYjYxNjBmMmIxZDY5NjBlMWI5NjA5NGQ3M2VkNDNlZTgxODM4YWQ0ZDgzZTEzZmY0NjJkMDAwIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImQ5SUUxN3RqZ3pvdmY5dVlTVzlGYkE9PSIsInZhbHVlIjoiOVRmWE9NMThLNGpKc01KOW9IZTFkWjVvMnpJbTVUdGduVXU3TGVGNFlaQ041NUQ2N1h5a1NHdHIvckRINlpiN2MzS2daL2lZVStMd2QvU01la0REV1p3QnZJU0UvdWlrVHBUbFlkS1lhaW0wMzRVcFBjakNpMXluVFQzVmlhcVciLCJtYWMiOiI2ZjZjNTIyMjBhZDQ0Mzk4OGQ3M2IyMWU3OWY4YTQxYzZkN2UxODQ3ZDVmZTFhZGUyNGFmMzcwYjA3OTg1MDM1IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460424329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2086165307 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086165307\", {\"maxDepth\":0})</script>\n"}}