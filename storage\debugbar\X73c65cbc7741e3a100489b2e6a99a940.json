{"__meta": {"id": "X73c65cbc7741e3a100489b2e6a99a940", "datetime": "2025-07-20 07:46:21", "utime": **********.796951, "method": "GET", "uri": "/stok-sparepart/export-form", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.362565, "end": **********.796972, "duration": 0.43440699577331543, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.362565, "relative_start": 0, "end": **********.673197, "relative_end": **********.673197, "duration": 0.31063199043273926, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.673214, "relative_start": 0.31064891815185547, "end": **********.796975, "relative_end": 2.86102294921875e-06, "duration": 0.12376093864440918, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25401696, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "stok-sparepart.export-form", "param_count": null, "params": [], "start": **********.785419, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/stok-sparepart/export-form.blade.phpstok-sparepart.export-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fstok-sparepart%2Fexport-form.blade.php&line=1", "ajax": false, "filename": "export-form.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.789138, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.79034, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.791994, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET stok-sparepart/export-form", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\StokSparepartController@exportForm", "namespace": null, "prefix": "", "where": [], "as": "stok-sparepart.export-form", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=159\" onclick=\"\">app/Http/Controllers/StokSparepartController.php:159-170</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02254, "accumulated_duration_str": "22.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.734128, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.7384522, "duration": 0.02205, "duration_str": "22.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 97.826}, {"sql": "select `id`, `nama`, `jenis_kapal_id` as `jenis` from `kapal`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 163}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.766489, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "StokSparepartController.php:163", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=163", "ajax": false, "filename": "StokSparepartController.php", "line": "163"}, "connection": "gema_kapal", "explain": null, "start_percent": 97.826, "width_percent": 2.174}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/stok-sparepart/export-form\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/stok-sparepart/export-form", "status_code": "<pre class=sf-dump id=sf-dump-1043950617 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1043950617\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1686680939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1686680939\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1599671571 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1599671571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ii91QjNlMFJjSTBRYXRPbTV2emNrN1E9PSIsInZhbHVlIjoiamJ4aW5KWjlnZkpuTjhFVUxoenRPYmlPTXJqaHd1TUJGdEF4c1lrMGFhaTNaZDZuU2UwYmFVR1VZREZGaWhncXBuVzNhNU9Wdjl2UVgxQVkrT3ZEaUxWVndMc0pzQUN3UUhKQ2V5S2pwa1JIRkxEeVk5cnZmVndJNkEzN2pER0MiLCJtYWMiOiIyNjliOTNmOGE5MzhlODFlNjZlYWVlMTYxNGI1ZDM5M2YwZDU4ZTgzNzVkMzJhMWQxN2MzNWJlNjg5MDQ0YjQ1IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImZ6dVlFZFp3M2lob2RFTExlWmdtc3c9PSIsInZhbHVlIjoiaFluV3orVXhGRjdKK1YwSCszN25mNWswY3hESzBhYXNMQXVNZCt5b0xBVWlpVU9nMDZEVGNadVdYK1FPNHBnRURLeGNPVkFXNHVwNHRVVDV0dE8wWmVjWEZRL0tKMmdBTUtSU3laNEZjWTFpWDl3L1l3MUNtSmtxdW84M3lRTkYiLCJtYWMiOiJmNmYyMDkwOTNiYzdhODE5NzRjYmE2YWE0ZmE3YWMzMjljYzkyMjYyNTVkNWExMzNjZWQwYjZiNzJhZDRiZTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xd646bANpl7rqaFjqnXFGwoyUtkp1VrQmXt91Gb7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2032134855 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:46:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkQ4c3hyenpvYk9MOWN3OElTcGZDeGc9PSIsInZhbHVlIjoiUkI0VnRzWEVPWElNV1NXT1hTRTZQQVUvbER0YVJuVzkvWEg5bElNdHBwWThsZFh2SGVRaXBiNGcwYjRiVmtoWnJUVld0ZlViSDQxc0xXM1JKVDNYUG9JQXRESDZhQk9HUTE0R2NuNC9oTUE3YUFjSlJteG9JbGpQNnhQNGptemgiLCJtYWMiOiJjMDdjNzdkODYyMDMyZjU4MzhmNGYzMWRiNmQyY2UxY2JjMGMwYzVjNThmZjIxMDc1NWE3YzI3NzcwMzFlYzk0IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:46:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Inp2WXZMeHpvNXJUWjVPU2NiTDNmNkE9PSIsInZhbHVlIjoiWkI0SFVKV0NNNzJwK1FlTko5VWVscTQrZENyYTBtU3VOSUo1NjA3dUFPTk5zeERRYVNWVUFrTlU5Q3kxTWRTeXk1eDdiWURYRUVFRW1RR1p5YmRGQWh4MGNhMFVDVHU1aEIxOE1hRjFvWjNBRWtvQVp3L1FQaytraTM5SzdNS3IiLCJtYWMiOiIzYTQ1YTBiYTVmMjI2ODgzZWEyNzgxMWE0YzZhYTIyYmRhMTUwOTNiYzQ0NTFhYjQyMjJhYjUyNDNjM2UzY2ZjIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:46:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkQ4c3hyenpvYk9MOWN3OElTcGZDeGc9PSIsInZhbHVlIjoiUkI0VnRzWEVPWElNV1NXT1hTRTZQQVUvbER0YVJuVzkvWEg5bElNdHBwWThsZFh2SGVRaXBiNGcwYjRiVmtoWnJUVld0ZlViSDQxc0xXM1JKVDNYUG9JQXRESDZhQk9HUTE0R2NuNC9oTUE3YUFjSlJteG9JbGpQNnhQNGptemgiLCJtYWMiOiJjMDdjNzdkODYyMDMyZjU4MzhmNGYzMWRiNmQyY2UxY2JjMGMwYzVjNThmZjIxMDc1NWE3YzI3NzcwMzFlYzk0IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:46:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Inp2WXZMeHpvNXJUWjVPU2NiTDNmNkE9PSIsInZhbHVlIjoiWkI0SFVKV0NNNzJwK1FlTko5VWVscTQrZENyYTBtU3VOSUo1NjA3dUFPTk5zeERRYVNWVUFrTlU5Q3kxTWRTeXk1eDdiWURYRUVFRW1RR1p5YmRGQWh4MGNhMFVDVHU1aEIxOE1hRjFvWjNBRWtvQVp3L1FQaytraTM5SzdNS3IiLCJtYWMiOiIzYTQ1YTBiYTVmMjI2ODgzZWEyNzgxMWE0YzZhYTIyYmRhMTUwOTNiYzQ0NTFhYjQyMjJhYjUyNDNjM2UzY2ZjIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:46:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032134855\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-737322111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://sikapal.test/stok-sparepart/export-form</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737322111\", {\"maxDepth\":0})</script>\n"}}