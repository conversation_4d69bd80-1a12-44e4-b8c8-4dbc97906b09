{"__meta": {"id": "Xa46429a6169cde866cc4315b18ba62ef", "datetime": "2025-07-20 07:26:05", "utime": 1752971165.226863, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752971164.82513, "end": 1752971165.226887, "duration": 0.40175700187683105, "duration_str": "402ms", "measures": [{"label": "Booting", "start": 1752971164.82513, "relative_start": 0, "end": 1752971165.119156, "relative_end": 1752971165.119156, "duration": 0.29402589797973633, "duration_str": "294ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752971165.119171, "relative_start": 0.2940409183502197, "end": 1752971165.22689, "relative_end": 3.0994415283203125e-06, "duration": 0.10771918296813965, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23738088, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1752971165.173461, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=11\" onclick=\"\">app/Http/Controllers/Auth/LoginController.php:11-14</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DvzKpXf3twh86OzP5SEv5t8FIgpl7SSI8971pMp2", "url": "array:1 [\n  \"intended\" => \"http://sikapal.test/stok-sparepart/export-form\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-102357389 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-102357389\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1708228506 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1708228506\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1461803168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1461803168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976729726 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJIWFE1ZUFTaGVZRXpYYnhOa3E0d2c9PSIsInZhbHVlIjoia2NCRGxvUW9PVEZPZzFjRWpDcWUxV3E2ZUZlbGRzaktJMFJUSjVnc2V2K1ptTWZZRjNVN0NhYUNwZkQ5ZWtvQXY5a1VVN1p1N1B0SlI4QzNIbkJMUXVvSzVobHpYN0pZdDdFOEVlcWN5UmlndWY4eUJ0ckE2b05pdDhaSHluZVgiLCJtYWMiOiI4YTg0Y2ZlOTNkNjQzMjlkNGRiNDNiMzA5ZmQ4MGFhNmQwYTRhYWY0OTI5MzM1MGI4YTFmYzllOGMxNTYyMGU0IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IjRkaVhXTFNzTDJOVU82ZlR4bEdidVE9PSIsInZhbHVlIjoiL1JRbW95QzFBRURzZXVBd2ZsMStrV01odVZ5ZGtvQ1FoNkFtUnRIYXpoLy8vMk13elY0N3VOcEJJdlhHK2NmMEFmZFA5cjRGRTh3ZzNaVm9NaU9oRS9ueHp1V0pZSHdDb2tibU1XQ0pMTGFUTnNkbHNNb1NIcFVmWjR6Si9sN2wiLCJtYWMiOiIwNjEzMTYzNmNlZjQ5MmVkNDlmZTQ4MzNlYzMwZjQwMjI4MDAxMjg1NDI4ZGY5NWNkMWRlZDQxMzg1ZjI1MWUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976729726\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DvzKpXf3twh86OzP5SEv5t8FIgpl7SSI8971pMp2</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">97Gi97yAdnkTpDKZvrHQIw6tprT8eNMRRLCYndVm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-140420262 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:26:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklBbmt0ZDZwK09GNEJsN1F1aXl6SEE9PSIsInZhbHVlIjoiVVhtWndmQ2x2eTYreWlROGk5S0xFUUcvNDFmU0hKVWw1OCtvMXJqcjBJWlFYWVhSZ3hPQUpVcThDN0RyRHl3ellaYlI0M25DclRVUFR2T3pSYzdaekNxQ0lUN3NTSm1MT05Obkx1bmFZd0xYa2MyU1lXelhZMUM5NXlXUTlqdWkiLCJtYWMiOiJmYmMwMjZkNGU2ODNkMjAzY2I5YTA4YmJmZmJmODVhMmE5NTcwMmJmMTczMjczMjQwYzU1YWM3ZTU0YWM5OTU3IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:26:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ii9SLzRzVzNtR3N0eE1PcFhnK0dBUEE9PSIsInZhbHVlIjoib3c4eFRVcnVReTE0cXlVZEVoU3BtZmhUTTFCTTJMVGExa3krOUlTVEdKc3VFaDEvU3RJdDB5M2VYVURDK21Pd0FoK2lhWEd0L08xNkpvRFkzN2N2YXFWY3VKalNiK0hmT291R1B0MXNVL29DQWVqUTA1S244ZjNza0FySEloQkUiLCJtYWMiOiI0ZjRmMTY3ZjdmNzVmMzQ0NmM2M2Q3NDgzZDRlZTU2YmE5ZTNkYzNiZjIxNDcwYjRhNzZjZWMyM2U5OTk3ZTk2IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:26:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklBbmt0ZDZwK09GNEJsN1F1aXl6SEE9PSIsInZhbHVlIjoiVVhtWndmQ2x2eTYreWlROGk5S0xFUUcvNDFmU0hKVWw1OCtvMXJqcjBJWlFYWVhSZ3hPQUpVcThDN0RyRHl3ellaYlI0M25DclRVUFR2T3pSYzdaekNxQ0lUN3NTSm1MT05Obkx1bmFZd0xYa2MyU1lXelhZMUM5NXlXUTlqdWkiLCJtYWMiOiJmYmMwMjZkNGU2ODNkMjAzY2I5YTA4YmJmZmJmODVhMmE5NTcwMmJmMTczMjczMjQwYzU1YWM3ZTU0YWM5OTU3IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:26:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ii9SLzRzVzNtR3N0eE1PcFhnK0dBUEE9PSIsInZhbHVlIjoib3c4eFRVcnVReTE0cXlVZEVoU3BtZmhUTTFCTTJMVGExa3krOUlTVEdKc3VFaDEvU3RJdDB5M2VYVURDK21Pd0FoK2lhWEd0L08xNkpvRFkzN2N2YXFWY3VKalNiK0hmT291R1B0MXNVL29DQWVqUTA1S244ZjNza0FySEloQkUiLCJtYWMiOiI0ZjRmMTY3ZjdmNzVmMzQ0NmM2M2Q3NDgzZDRlZTU2YmE5ZTNkYzNiZjIxNDcwYjRhNzZjZWMyM2U5OTk3ZTk2IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:26:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140420262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1760880207 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DvzKpXf3twh86OzP5SEv5t8FIgpl7SSI8971pMp2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://sikapal.test/stok-sparepart/export-form</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760880207\", {\"maxDepth\":0})</script>\n"}}