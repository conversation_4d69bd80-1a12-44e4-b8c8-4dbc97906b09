{"__meta": {"id": "X151f740a6eee006c7a9f9b07194ee04b", "datetime": "2025-07-20 07:17:09", "utime": 1752970629.001359, "method": "GET", "uri": "/admin/kantor/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[07:17:08] LOG.info: Stok Kapal Kosong: {\n    \"query\": [],\n    \"result\": [\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 225,\n            \"nama_barang\": \"FUEL FILTER SCREEN\",\n            \"nomor_seri\": \"FUEL FILTER SCREEN\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 226,\n            \"nama_barang\": \"PRESSURE TRANSMITER\",\n            \"nomor_seri\": \"04541 - 90200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 227,\n            \"nama_barang\": \"GASKET CYL HEAD (TSP)\",\n            \"nomor_seri\": \"3750112200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 228,\n            \"nama_barang\": \"PACKING, ROCKER CASE\",\n            \"nomor_seri\": \"3750441200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 229,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"3750402300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 230,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550710200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 231,\n            \"nama_barang\": \"SEAL, VALVE STEM\",\n            \"nomor_seri\": \"3750400900\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 232,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550531065\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 233,\n            \"nama_barang\": \"ORING LINER\",\n            \"nomor_seri\": \"3750732400\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 234,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704201\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 235,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.98213, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.673578, "end": 1752970629.001381, "duration": 0.3278028964996338, "duration_str": "328ms", "measures": [{"label": "Booting", "start": **********.673578, "relative_start": 0, "end": **********.887577, "relative_end": **********.887577, "duration": 0.21399903297424316, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.887591, "relative_start": 0.21401286125183105, "end": 1752970629.001384, "relative_end": 3.0994415283203125e-06, "duration": 0.11379313468933105, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25710368, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "admin_kantor.dashboard", "param_count": null, "params": [], "start": **********.991895, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/dashboard.blade.phpadmin_kantor.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.995108, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.996074, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.997384, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/kantor/dashboard", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\AdminKantorDashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "admin.kantor.dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=12\" onclick=\"\">app/Http/Controllers/AdminKantorDashboardController.php:12-168</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027270000000000003, "accumulated_duration_str": "27.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.928414, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.9330142, "duration": 0.01644, "duration_str": "16.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 60.286}, {"sql": "select count(*) as aggregate from `pembelian_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 15}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9532962, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:15", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=15", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "15"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.286, "width_percent": 6.124}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.957124, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:18", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=18", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "18"}, "connection": "gema_kapal", "explain": null, "start_percent": 66.41, "width_percent": 4.95}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9595032, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:22", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=22", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "22"}, "connection": "gema_kapal", "explain": null, "start_percent": 71.36, "width_percent": 1.65}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9609752, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:23", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=23", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 73.011, "width_percent": 1.76}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9625008, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=29", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "29"}, "connection": "gema_kapal", "explain": null, "start_percent": 74.771, "width_percent": 0.88}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.963673, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:35", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=35", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "35"}, "connection": "gema_kapal", "explain": null, "start_percent": 75.651, "width_percent": 1.21}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9649801, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:41", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=41", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "41"}, "connection": "gema_kapal", "explain": null, "start_percent": 76.861, "width_percent": 1.173}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.966226, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=42", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "42"}, "connection": "gema_kapal", "explain": null, "start_percent": 78.034, "width_percent": 1.173}, {"sql": "select count(*) as aggregate from `data_sparepart`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.967639, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:48", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=48", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "48"}, "connection": "gema_kapal", "explain": null, "start_percent": 79.208, "width_percent": 4.474}, {"sql": "select count(*) as aggregate from `data_sparepart` where month(`created_at`) = '06'", "type": "query", "params": [], "bindings": ["06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.96998, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:53", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=53", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "53"}, "connection": "gema_kapal", "explain": null, "start_percent": 83.682, "width_percent": 1.907}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where `created_at` >= '2025-07-06 07:17:08'", "type": "query", "params": [], "bindings": ["2025-07-06 07:17:08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.971557, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:60", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=60", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "60"}, "connection": "gema_kapal", "explain": null, "start_percent": 85.589, "width_percent": 1.503}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where `created_at` >= '2025-07-06 07:17:08'", "type": "query", "params": [], "bindings": ["2025-07-06 07:17:08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.972994, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:63", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=63", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "63"}, "connection": "gema_kapal", "explain": null, "start_percent": 87.092, "width_percent": 1.613}, {"sql": "select `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, (\nSELECT COALESCE(SUM(CASE\nWHEN jenis_transaksi = \"penerimaan_sparepart_kapal\" THEN jumlah\nWHEN jenis_transaksi = \"pemakaian_sparepart_kapal\" THEN -jumlah\nELSE 0\nEND), 0)\nFROM riwayat_stok_kantor\nWHERE id_barang = ds.id\nAND jenis_transaksi IN (\"penerimaan_sparepart_kapal\", \"pemakaian_sparepart_kapal\")\n) as stok_tersedia, (\nSELECT GROUP_CONCAT(DISTINCT pk.kapal_id)\nFROM pengeluaran_kantor pk\nJOIN pembelian_kantor pmk ON pmk.id = pk.id_pembelian\nWHERE pmk.id_barang = ds.id\nAND pk.jumlah > 0\n) as kapal_dengan_stok from `data_sparepart` as `ds` where exists (select 1 from `riwayat_stok_kantor` where riwayat_stok_kantor.id_barang = ds.id and `jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal')) having `stok_tersedia` <= 0 and `kapal_dengan_stok` = null", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", 0, null], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 99}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.975688, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:99", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=99", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 88.706, "width_percent": 7.004}, {"sql": "select `k`.`id` as `kapal_id`, `k`.`nama` as `nama_kapal`, `k`.`jenis_kapal_id`, `ds`.`id` as `id_barang`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, COALESCE(SUM(prk.jumlah), 0) as stok_tersedia, MAX(pk.jumlah) as jumlah_pengeluaran from `kapal` as `k` inner join `pengeluaran_kantor` as `pk` on `k`.`id` = `pk`.`kapal_id` inner join `pembelian_kantor` as `pmk` on `pmk`.`id` = `pk`.`id_pembelian` inner join `data_sparepart` as `ds` on `pmk`.`id_barang` = `ds`.`id` left join `penerimaan_kapal` as `prk` on `k`.`id` = `prk`.`kapal_id` and `pk`.`id` = `prk`.`pengeluaran_kantor_id` where exists (select 1 from `pengeluaran_kantor` inner join `pembelian_kantor` on `pembelian_kantor`.`id` = `pengeluaran_kantor`.`id_pembelian` where `pengeluaran_kantor`.`kapal_id` = `k`.`id` and `pembelian_kantor`.`id_barang` = `ds`.`id`) group by `k`.`id`, `k`.`nama`, `k`.`jenis_kapal_id`, `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan` having `stok_tersedia` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 142}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9791062, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:142", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 142}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=142", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "142"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.71, "width_percent": 4.29}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/admin/kantor/dashboard\"\n]"}, "request": {"path_info": "/admin/kantor/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1837901932 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1837901932\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-718667446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-718667446\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-412628030 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-412628030\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1191757290 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">http://sikapal.test/pembelian-kantor?page=1&amp;search=PRESSURE+TRANSMITER%09</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InlSZEpvWmxEa0NkRHV5YVJsd1NrM2c9PSIsInZhbHVlIjoiOHZ2ODRUUHByeWJTOGlMeVhDOUQ5WWpqeHhlVGgwRHpCU1ZFQnl2STN2QjU5ckVXZmpMYjI1TTlDY0ZKTXZqRDJzSzI2bHk0NW5BcmpJU3oyUjdEN0Vta2RRRkpia3g4MEdRNVZzUERYMUZYTVZudE9zcS9vUGFWMW9qTnhzdGkiLCJtYWMiOiJmNjI2OGJmNzBlNTFjYjViMzA2NzY5NWUzMmY1ZGVmOGVkZjRhNTY4OWJmMzYyMmU5ZDEyNTVhZDY5OGY3NThlIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IlVtQW0vNHZXOXF2NzcrQ2JwY0FZQkE9PSIsInZhbHVlIjoiSDRoQnlBRHRtQnFIakVwVDBlM2MxRGpYaG4rcCtTV0pqVGQ0anBhNmlsVndSV3lXTzU5ZVc5ZldtS204V0EyTDhZUzJ2SFdyMlo4NVJFZFZBUlhpK3JGUDFQSjNpRGNDRDNIYXU0b25OZU9WWVRSYk82bDNRWWtOYjNMcG9LRWciLCJtYWMiOiIyZjg5NjJhNjcwMDViNzllNjBhMDQ2MWZjMjY1YmRkNjkyZjdiNmQ5YWQ5ZDc0MjgxZGJhZTEzMjlmMThiZjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191757290\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1549234188 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549234188\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1635501014 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:17:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklndjFiR0htQ1VwZGdqbGcwUERUSXc9PSIsInZhbHVlIjoianFhQTdrUzdPUGcyYkpYOENlTkh6c0pWTGhlVlRyUXRCWXdQbEFINVBKSFF2RDAzMjlrQllUT1VwQVlxalpPV2ZhTC9uZTR6enI3Z2JJbXNoV3lwbEpGeE5nWnN2QUVseFRVMStrTUtJaUp0b21MeXI0UDlUbi9yVlU0S3lnbDEiLCJtYWMiOiI2Yjc2NTQ4NGJjZmY1MWVmODQ0NjY0NDUwMmI0ZjE5ZDQ1YmI0YjkzNTNjYjc4Mjc5NGMzNjllNGQyZDM2MzkzIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:17:08 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ijk5UTNOeFF3U0NjT1JRYjVlQkozVEE9PSIsInZhbHVlIjoiS3ZMUkFIbUkwdnNOZHNNK29zc2djOTVRS3pGZ0RJK1IzdnJaUHJseDRPMnYvcVdNdnFoQ3Nka1FmY1hOdGRZRmRrMjZNZGZZNEtncUZ2VjNhaU13ZG56U2JNdWFZUVFoVlE4L29WaThjRTBiNDlyeXBxSlM0WVAxRDFLN0FWZFciLCJtYWMiOiI3NGRmYzNhMjMxYWYxNGI5YmE1NDFjOGYwZWEwMzZlOTM2OTRhYThkNWM2ZDM0NjY3NDk0YWFiN2ZkZDRmM2VkIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:17:08 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklndjFiR0htQ1VwZGdqbGcwUERUSXc9PSIsInZhbHVlIjoianFhQTdrUzdPUGcyYkpYOENlTkh6c0pWTGhlVlRyUXRCWXdQbEFINVBKSFF2RDAzMjlrQllUT1VwQVlxalpPV2ZhTC9uZTR6enI3Z2JJbXNoV3lwbEpGeE5nWnN2QUVseFRVMStrTUtJaUp0b21MeXI0UDlUbi9yVlU0S3lnbDEiLCJtYWMiOiI2Yjc2NTQ4NGJjZmY1MWVmODQ0NjY0NDUwMmI0ZjE5ZDQ1YmI0YjkzNTNjYjc4Mjc5NGMzNjllNGQyZDM2MzkzIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:17:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ijk5UTNOeFF3U0NjT1JRYjVlQkozVEE9PSIsInZhbHVlIjoiS3ZMUkFIbUkwdnNOZHNNK29zc2djOTVRS3pGZ0RJK1IzdnJaUHJseDRPMnYvcVdNdnFoQ3Nka1FmY1hOdGRZRmRrMjZNZGZZNEtncUZ2VjNhaU13ZG56U2JNdWFZUVFoVlE4L29WaThjRTBiNDlyeXBxSlM0WVAxRDFLN0FWZFciLCJtYWMiOiI3NGRmYzNhMjMxYWYxNGI5YmE1NDFjOGYwZWEwMzZlOTM2OTRhYThkNWM2ZDM0NjY3NDk0YWFiN2ZkZDRmM2VkIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:17:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635501014\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-637119100 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637119100\", {\"maxDepth\":0})</script>\n"}}