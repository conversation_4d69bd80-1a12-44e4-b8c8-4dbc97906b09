{"__meta": {"id": "Xea47640a27159c0113f61da2a670ab00", "datetime": "2025-07-20 07:15:19", "utime": 1752970519.102359, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752970517.201406, "end": 1752970519.102385, "duration": 1.9009790420532227, "duration_str": "1.9s", "measures": [{"label": "Booting", "start": 1752970517.201406, "relative_start": 0, "end": 1752970517.513535, "relative_end": 1752970517.513535, "duration": 0.31212902069091797, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752970517.513551, "relative_start": 0.3121449947357178, "end": 1752970519.102388, "relative_end": 2.86102294921875e-06, "duration": 1.588836908340454, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23738536, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1752970519.015904, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=11\" onclick=\"\">app/Http/Controllers/Auth/LoginController.php:11-14</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fEQaFvUquJVKqXsR8nF5T4z7woyLWnYBkn27nWHO", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1648937220 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1648937220\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1107321426 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1107321426\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1594343035 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1594343035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703631682 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InlNUnkzK1diMnZNWnovZVljdXlvNlE9PSIsInZhbHVlIjoidHRPVGFBZHB0S0dtYW42WVhaSjQrdWVpaXpiR1JvR2JMckU0OExGVmY5cy9LNk13bHVvQ1VBRElGTXhxZkl3V3dMSW56aDZxSitDTExSeW0xTlJQbHFpUkpxdm5ZYU1KVTlBOVREbHJDR0tQcGIySGhyMmJVNHg5U0JGZ2IrbDgiLCJtYWMiOiIzMzljNDQwZWJmNTcxMTY2MTNlYjY5ZDQ2YzVjNGI3ZDJmMTE3MDgyMGY5OGE0Y2EwNTZiM2Q1ZjFiOTQ2YjU3IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IklTY1pnZUJ4YlQ3SnBXNjhDZ0NEZnc9PSIsInZhbHVlIjoiM014cmNwTWlpVXFRanBNbTFmMkljVDJPSWlTMFFuSnc5MnVQaFNzN2hHYm5QVEdaVEtLWUFtb1RhT1RMWDZlQVB6RDV2aHdaNUpRalNWTFRYc3JKMk02bkg0MzVnU0JxeTZvQmozQy9vSDk1VTZlL0Y2ZXhmM1lNYlZEc0RLNEciLCJtYWMiOiI2YmUwMWQ0YWNlM2M1NTYzNjUzODYwMGNhY2Q2OGJjY2MzMWUxYzRhNDBkYzAzYzM2YmE2ZWVjZTgzMWE3MDYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703631682\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1425571952 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fEQaFvUquJVKqXsR8nF5T4z7woyLWnYBkn27nWHO</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FhiOMlTqQ3BRIhj5UwgqdwOoV5YJvXbQXARMvzZg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425571952\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1399021779 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:15:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikc0NWtKVGJselRKT1dKTk9xM2xiTkE9PSIsInZhbHVlIjoiNUxvRklETzV1b2Y4MGt1aUYxTjBTN0ZnOCtaMHU4eUNZZDgzY1AwRDA2M2xSZ1lrUGY1SERCektMVENUTTJoZ0VYdDk0NHFuNjZEaHZvUGpiQkxpRDU3NFF3QVl5dXhyYTJGeE1zdTRWVng4SVJlZnZtSVdaWlB3V3ZvQzdJMmgiLCJtYWMiOiI3ZWU5ZmU0N2JmY2IxYmJiYWY5NjdkOWI1MGQzMjk0YmM1OWFiNzJkMmE1YjFlYWM4NjYyNTk3YmFlNWZlM2M5IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ild6YWhBOEZ5NkwrUFk5SlN2dTc3TWc9PSIsInZhbHVlIjoieFo2bmYwWkdnSnd1dlZHOVhTRFZEM1FMTmowb0pGUi9KQU9lZXJmZTZzZTJMMlYrekhYbU4yZVFTOCtEZ3I0eXlaeGh5QUxkbk5yWFU3WmFzTGtCNFVsRmtYVTF1ZjY5dWREQk5CWG1HUWhtZmJsTDVsdjZOMWd5M1FUWnBrb0giLCJtYWMiOiIzNDQzMTVjMmRlMzdmOWRhMzVkYjA5N2IxYzkwMGYyNWE3MWFhZmYyNzc2ZWJlMGJmZjNmODE5ZWQ2MWQ2NmRmIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikc0NWtKVGJselRKT1dKTk9xM2xiTkE9PSIsInZhbHVlIjoiNUxvRklETzV1b2Y4MGt1aUYxTjBTN0ZnOCtaMHU4eUNZZDgzY1AwRDA2M2xSZ1lrUGY1SERCektMVENUTTJoZ0VYdDk0NHFuNjZEaHZvUGpiQkxpRDU3NFF3QVl5dXhyYTJGeE1zdTRWVng4SVJlZnZtSVdaWlB3V3ZvQzdJMmgiLCJtYWMiOiI3ZWU5ZmU0N2JmY2IxYmJiYWY5NjdkOWI1MGQzMjk0YmM1OWFiNzJkMmE1YjFlYWM4NjYyNTk3YmFlNWZlM2M5IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ild6YWhBOEZ5NkwrUFk5SlN2dTc3TWc9PSIsInZhbHVlIjoieFo2bmYwWkdnSnd1dlZHOVhTRFZEM1FMTmowb0pGUi9KQU9lZXJmZTZzZTJMMlYrekhYbU4yZVFTOCtEZ3I0eXlaeGh5QUxkbk5yWFU3WmFzTGtCNFVsRmtYVTF1ZjY5dWREQk5CWG1HUWhtZmJsTDVsdjZOMWd5M1FUWnBrb0giLCJtYWMiOiIzNDQzMTVjMmRlMzdmOWRhMzVkYjA5N2IxYzkwMGYyNWE3MWFhZmYyNzc2ZWJlMGJmZjNmODE5ZWQ2MWQ2NmRmIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399021779\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2080766329 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fEQaFvUquJVKqXsR8nF5T4z7woyLWnYBkn27nWHO</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080766329\", {\"maxDepth\":0})</script>\n"}}