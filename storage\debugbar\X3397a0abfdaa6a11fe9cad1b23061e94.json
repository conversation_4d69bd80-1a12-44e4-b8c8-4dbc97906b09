{"__meta": {"id": "X3397a0abfdaa6a11fe9cad1b23061e94", "datetime": "2025-07-20 07:15:16", "utime": **********.818105, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752970500.518578, "end": **********.818135, "duration": 16.299556970596313, "duration_str": "16.3s", "measures": [{"label": "Booting", "start": 1752970500.518578, "relative_start": 0, "end": **********.86959, "relative_end": **********.86959, "duration": 13.351011991500854, "duration_str": "13.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.871973, "relative_start": 13.***************, "end": **********.818138, "relative_end": 2.86102294921875e-06, "duration": 2.***************, "duration_str": "2.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, guest", "uses": "Closure() {#290\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#287 …}\n  file: \"C:\\laragon\\www\\sikapal\\routes\\web.php\"\n  line: \"35 to 37\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Froutes%2Fweb.php&line=35\" onclick=\"\">routes/web.php:35-37</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fEQaFvUquJVKqXsR8nF5T4z7woyLWnYBkn27nWHO", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-94096581 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-94096581\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-693246234 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-693246234\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-172797988 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172797988\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-610165609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610165609\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1418070236 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:15:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlNUnkzK1diMnZNWnovZVljdXlvNlE9PSIsInZhbHVlIjoidHRPVGFBZHB0S0dtYW42WVhaSjQrdWVpaXpiR1JvR2JMckU0OExGVmY5cy9LNk13bHVvQ1VBRElGTXhxZkl3V3dMSW56aDZxSitDTExSeW0xTlJQbHFpUkpxdm5ZYU1KVTlBOVREbHJDR0tQcGIySGhyMmJVNHg5U0JGZ2IrbDgiLCJtYWMiOiIzMzljNDQwZWJmNTcxMTY2MTNlYjY5ZDQ2YzVjNGI3ZDJmMTE3MDgyMGY5OGE0Y2EwNTZiM2Q1ZjFiOTQ2YjU3IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:16 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IklTY1pnZUJ4YlQ3SnBXNjhDZ0NEZnc9PSIsInZhbHVlIjoiM014cmNwTWlpVXFRanBNbTFmMkljVDJPSWlTMFFuSnc5MnVQaFNzN2hHYm5QVEdaVEtLWUFtb1RhT1RMWDZlQVB6RDV2aHdaNUpRalNWTFRYc3JKMk02bkg0MzVnU0JxeTZvQmozQy9vSDk1VTZlL0Y2ZXhmM1lNYlZEc0RLNEciLCJtYWMiOiI2YmUwMWQ0YWNlM2M1NTYzNjUzODYwMGNhY2Q2OGJjY2MzMWUxYzRhNDBkYzAzYzM2YmE2ZWVjZTgzMWE3MDYzIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:16 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlNUnkzK1diMnZNWnovZVljdXlvNlE9PSIsInZhbHVlIjoidHRPVGFBZHB0S0dtYW42WVhaSjQrdWVpaXpiR1JvR2JMckU0OExGVmY5cy9LNk13bHVvQ1VBRElGTXhxZkl3V3dMSW56aDZxSitDTExSeW0xTlJQbHFpUkpxdm5ZYU1KVTlBOVREbHJDR0tQcGIySGhyMmJVNHg5U0JGZ2IrbDgiLCJtYWMiOiIzMzljNDQwZWJmNTcxMTY2MTNlYjY5ZDQ2YzVjNGI3ZDJmMTE3MDgyMGY5OGE0Y2EwNTZiM2Q1ZjFiOTQ2YjU3IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IklTY1pnZUJ4YlQ3SnBXNjhDZ0NEZnc9PSIsInZhbHVlIjoiM014cmNwTWlpVXFRanBNbTFmMkljVDJPSWlTMFFuSnc5MnVQaFNzN2hHYm5QVEdaVEtLWUFtb1RhT1RMWDZlQVB6RDV2aHdaNUpRalNWTFRYc3JKMk02bkg0MzVnU0JxeTZvQmozQy9vSDk1VTZlL0Y2ZXhmM1lNYlZEc0RLNEciLCJtYWMiOiI2YmUwMWQ0YWNlM2M1NTYzNjUzODYwMGNhY2Q2OGJjY2MzMWUxYzRhNDBkYzAzYzM2YmE2ZWVjZTgzMWE3MDYzIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418070236\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1497740493 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fEQaFvUquJVKqXsR8nF5T4z7woyLWnYBkn27nWHO</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497740493\", {\"maxDepth\":0})</script>\n"}}