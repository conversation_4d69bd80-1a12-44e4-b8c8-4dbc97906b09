{"__meta": {"id": "Xb47b805e43ad0da14f7e21b33957a745", "datetime": "2025-07-20 07:47:34", "utime": 1752972454.47578, "method": "GET", "uri": "/stok-sparepart/export?kapal_id=4&date_range=01-01-2025+sampai+01-07-2025&tanggal_awal=2025-01-01&tanggal_akhir=2025-07-01&type=pdf", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752972451.626101, "end": 1752972454.47581, "duration": 2.8497090339660645, "duration_str": "2.85s", "measures": [{"label": "Booting", "start": 1752972451.626101, "relative_start": 0, "end": 1752972451.974953, "relative_end": 1752972451.974953, "duration": 0.34885191917419434, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752972451.974979, "relative_start": 0.3488779067993164, "end": 1752972454.475814, "relative_end": 4.0531158447265625e-06, "duration": 2.5008351802825928, "duration_str": "2.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 74811568, "peak_usage_str": "71MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "stok-sparepart.pdf", "param_count": null, "params": [], "start": **********.11839, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/stok-sparepart/pdf.blade.phpstok-sparepart.pdf", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fstok-sparepart%2Fpdf.blade.php&line=1", "ajax": false, "filename": "pdf.blade.php", "line": "?"}}]}, "route": {"uri": "GET stok-sparepart/export", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\StokSparepartController@export", "namespace": null, "prefix": "", "where": [], "as": "stok-sparepart.export", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=172\" onclick=\"\">app/Http/Controllers/StokSparepartController.php:172-300</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026010000000000002, "accumulated_duration_str": "26.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.040921, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.047399, "duration": 0.0238, "duration_str": "23.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 91.503}, {"sql": "select count(*) as aggregate from `kapal` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 875}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 658}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 457}], "start": **********.0945, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "gema_kapal", "explain": null, "start_percent": 91.503, "width_percent": 2.076}, {"sql": "select * from `kapal` where `id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 182}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.097741, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StokSparepartController.php:182", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=182", "ajax": false, "filename": "StokSparepartController.php", "line": "182"}, "connection": "gema_kapal", "explain": null, "start_percent": 93.579, "width_percent": 1.653}, {"sql": "select `rsk`.`created_at` as `tanggal`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, `rsk`.`jenis_transaksi`, `rsk`.`jumlah`, `rsk`.`stok_sebelum`, `rsk`.`stok_setelah`, `rsk`.`keterangan` from `riwayat_stok_kantor` as `rsk` inner join `data_sparepart` as `ds` on `rsk`.`id_barang` = `ds`.`id` where `rsk`.`jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal') and `rsk`.`kapal_id` = '4' and `rsk`.`created_at` between '2025-01-01 00:00:00' and '2025-07-01 23:59:59' order by `rsk`.`created_at` desc", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", "4", "2025-01-01 00:00:00", "2025-07-01 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 232}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.099829, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "StokSparepartController.php:232", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=232", "ajax": false, "filename": "StokSparepartController.php", "line": "232"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.233, "width_percent": 4.767}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/stok-sparepart/export?date_range=01-01-2025%20sampai%2001-07-2025&kapal_id=4&tanggal_akhir=2025-07-01&tanggal_awal=2025-01-01&type=pdf\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/stok-sparepart/export", "status_code": "<pre class=sf-dump id=sf-dump-1208353734 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1208353734\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/pdf", "request_query": "<pre class=sf-dump id=sf-dump-2021476399 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>kapal_id</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>date_range</span>\" => \"<span class=sf-dump-str title=\"28 characters\">01-01-2025 sampai 01-07-2025</span>\"\n  \"<span class=sf-dump-key>tanggal_awal</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-01-01</span>\"\n  \"<span class=sf-dump-key>tanggal_akhir</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-01</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pdf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021476399\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1613347599 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613347599\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-503275496 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://sikapal.test/stok-sparepart/export-form</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InphcndzOUlUdWRYNnhKWTQyTzlpUEE9PSIsInZhbHVlIjoiVmhaQTNTRVozQkFlZjJDTVNqUVJoQ1dBUk1ZR3hWOHlrZWRvblFqOEJsZjBxZEtmWTNVYkx6WjBsbEZWcnlpSFY0dnZ5RDBYWjRuMTcvVzRFcTcraUorRW9zaW5wWlp4VkZxN2NhOVppZEVQRkwrS1ppcUpabFlUNTBsTCtYV3kiLCJtYWMiOiIzNDBkMWQwZWY3NGE4YWMyMTMwMjhhNjlkYjU2MjI0MzkxMGVmMTBhZDczNWI5OTFjMGNmY2E3MWYxMmFlODRiIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6Inl6Vmt5SDNvRkJxMlllQWFpclA5dmc9PSIsInZhbHVlIjoibCtFUDZwZnBMOFdyRTAzSFZMK01DWEt5bFRIajdaYjRsOGFJV2x6a2hvd2NHSjJwdHUvWHlURXJXeG56L2k4U1VwMG96ZEtQdGdKdGpwZFc4ZVZnWVFnN2dWeXRDWVFIaUdCS1cwRllkNzBQcVZLNVE3WjVRL1VWaHdjSkRxQ0MiLCJtYWMiOiIzYWEyNzE1N2UxMjY5MGEzMGRjZDg4OWZlNWM3YTFlYmExZDM0NWZlZjdlMjNjMDJiMTA1YTBkNDM2YTBiYmZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503275496\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-429177771 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xd646bANpl7rqaFjqnXFGwoyUtkp1VrQmXt91Gb7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429177771\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"112 characters\">attachment; filename=&quot;Data Sparepart dan Amprahan yang Ada di Kapal TB. GEMA 201 (01-01-2025 sd 01-07-2025).pdf&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">50632</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:47:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNWcUFIT0VxNEdId1lzbzJydmRGWnc9PSIsInZhbHVlIjoidlpha1NWbGprNm0ybGZxWFVUK1VnQi9IMUNwN1U0WTN5dDJ3OXZTSEVkUk1MTFdJSE9GRzk5Z1ZFR1JqWHFxM3dld211NzQvU3N5TkMyODVXMlQvaDZFZ3NJRnp3NWp2SmlhMXJQWTJ4eU9abHZxSGEwTmNteVNmUUdpVDdBS24iLCJtYWMiOiJiYjk3NTZiOWVhMmZmZTU2NjVmMmNjNDI4YTMxNTQ0N2UzZDNkMDA2NjZhMmFiYmY5ZTIyOTQzOTRlODlmOTVmIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:47:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IjFzM0Z2ZEI5c1ZCcEpUUWxvQ1I1RXc9PSIsInZhbHVlIjoianNYMGFpY1ZFK3ZTdVJab1ByMHR0OGlUSXY1VlNsNWdKT3pYclhHQlN2R1g0cHhxeC9VL04rQUY4c3RPKzh0TlJ4NGlmTHJqSXVCRzdyV2xpTXZnZ0RuUXB3ZVMwOVJwekdFY3FuZ0srUTVXVFBJSmtnaUZXcENNbnd3NmsvWkciLCJtYWMiOiJlNDgxNGU4MmQ0YTViNWIzMzBkZjMzN2I1NzAyZTg2M2MyYWIzMTg1MTBkYWMxMzBjYzA5ZWNmNjE2YTg4ZDBlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:47:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNWcUFIT0VxNEdId1lzbzJydmRGWnc9PSIsInZhbHVlIjoidlpha1NWbGprNm0ybGZxWFVUK1VnQi9IMUNwN1U0WTN5dDJ3OXZTSEVkUk1MTFdJSE9GRzk5Z1ZFR1JqWHFxM3dld211NzQvU3N5TkMyODVXMlQvaDZFZ3NJRnp3NWp2SmlhMXJQWTJ4eU9abHZxSGEwTmNteVNmUUdpVDdBS24iLCJtYWMiOiJiYjk3NTZiOWVhMmZmZTU2NjVmMmNjNDI4YTMxNTQ0N2UzZDNkMDA2NjZhMmFiYmY5ZTIyOTQzOTRlODlmOTVmIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:47:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IjFzM0Z2ZEI5c1ZCcEpUUWxvQ1I1RXc9PSIsInZhbHVlIjoianNYMGFpY1ZFK3ZTdVJab1ByMHR0OGlUSXY1VlNsNWdKT3pYclhHQlN2R1g0cHhxeC9VL04rQUY4c3RPKzh0TlJ4NGlmTHJqSXVCRzdyV2xpTXZnZ0RuUXB3ZVMwOVJwekdFY3FuZ0srUTVXVFBJSmtnaUZXcENNbnd3NmsvWkciLCJtYWMiOiJlNDgxNGU4MmQ0YTViNWIzMzBkZjMzN2I1NzAyZTg2M2MyYWIzMTg1MTBkYWMxMzBjYzA5ZWNmNjE2YTg4ZDBlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:47:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1096841536 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"154 characters\">http://sikapal.test/stok-sparepart/export?date_range=01-01-2025%20sampai%2001-07-2025&amp;kapal_id=4&amp;tanggal_akhir=2025-07-01&amp;tanggal_awal=2025-01-01&amp;type=pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096841536\", {\"maxDepth\":0})</script>\n"}}