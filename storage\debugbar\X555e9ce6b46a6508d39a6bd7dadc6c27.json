{"__meta": {"id": "X555e9ce6b46a6508d39a6bd7dadc6c27", "datetime": "2025-07-20 07:39:19", "utime": **********.166828, "method": "GET", "uri": "/stok-sparepart/export-form", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752971958.672487, "end": **********.166856, "duration": 0.4943690299987793, "duration_str": "494ms", "measures": [{"label": "Booting", "start": 1752971958.672487, "relative_start": 0, "end": 1752971958.965552, "relative_end": 1752971958.965552, "duration": 0.29306507110595703, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752971958.965567, "relative_start": 0.29308009147644043, "end": **********.166859, "relative_end": 2.86102294921875e-06, "duration": 0.20129179954528809, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25599256, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "stok-sparepart.export-form", "param_count": null, "params": [], "start": **********.066076, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/stok-sparepart/export-form.blade.phpstok-sparepart.export-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fstok-sparepart%2Fexport-form.blade.php&line=1", "ajax": false, "filename": "export-form.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.159075, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.160251, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.161951, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET stok-sparepart/export-form", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\StokSparepartController@exportForm", "namespace": null, "prefix": "", "where": [], "as": "stok-sparepart.export-form", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=159\" onclick=\"\">app/Http/Controllers/StokSparepartController.php:159-170</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01769, "accumulated_duration_str": "17.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.023823, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.028165, "duration": 0.01717, "duration_str": "17.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 97.06}, {"sql": "select `id`, `nama`, `jenis_kapal_id` as `jenis` from `kapal`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 163}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.050307, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "StokSparepartController.php:163", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/StokSparepartController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\StokSparepartController.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FStokSparepartController.php&line=163", "ajax": false, "filename": "StokSparepartController.php", "line": "163"}, "connection": "gema_kapal", "explain": null, "start_percent": 97.06, "width_percent": 2.94}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/stok-sparepart/export-form\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"path_info": "/stok-sparepart/export-form", "status_code": "<pre class=sf-dump id=sf-dump-217054978 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-217054978\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-471575421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-471575421\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-482451238 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-482451238\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1104398310 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkF0QmJOK1lsYkM3M2lidUMzS2hya0E9PSIsInZhbHVlIjoiUWtDRVdGMGFMTCtQcXhoLzl6OVZVUEFOU2VqYlNRY3A1eHRzVkZ1ait0aDNyYXUyNGIzN0pUQm5DVEJnWURRZFBsbm14Q3RGbTJFVEVuVkUrWk9RZCtsVlNGTnZ1U3VRMnZ0cnZuZnJxb0g1amUrUFU5YXFVck5TYURQbGRrRGciLCJtYWMiOiIzYjFmMTdhNzE2OGIyYWU4MTczYTMxNWIxODljNTE3NmJhZDlmNjUyYzA2NDZiMzQ2ZDNjZjI1ODZiNjY1ZDBjIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IlRtMDllcmtMK0QycEkzc0ZCTG9OaFE9PSIsInZhbHVlIjoiMzZwWWZaaGFIUUlxcytlWHIrNEQ1MkhpbDZmUFJnZDN0K1cwTG1xV3p1TmZ5NjBTRWVwZXVzaThsVVpRaCtVVjkrdkJiTE9WMldTR0pRME8wZ1pET25CMDNXbTZZdHZHRE1HUmxnRXl2YW9GTUkrZmsyUFR6MFFGaVpBRmtRczUiLCJtYWMiOiI5YThiYmFkZTZjOTlkZTk3ODE1NmIxODc3ZTBmZjM3NGQ3NTM1ODkyN2ZhMTMyZWE0MWFmNmI1OWQyYTIwOTUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104398310\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-14371386 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xd646bANpl7rqaFjqnXFGwoyUtkp1VrQmXt91Gb7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14371386\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:39:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhhclJ0UVBlTHR2RWtxTWhpWmREWVE9PSIsInZhbHVlIjoiVkNDRGRLekpYOHpjakJyZ2JXQXRTcFV1Tm1jd21qUDNoTjBNUnlleklma0JjMGpCS1puM21nbUVxYm11bklzMWVCMENWMDRUWWVkWi9BRCsyK0hRZjc1Nk8rUTIzMGc4UjZRM203Nm1wOGlqbTVPS0FRQU5VT0RDaXExT3FuNlUiLCJtYWMiOiIzZDA0YWU1Y2U3MzMzNDY0NGQxMGNjYTFkZjljYzI5NmJkNzllNWYxYjlmN2Q2YWQ3ZjgwMWY3ZjdlZTlhYTBkIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:39:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImkvYTVtNnhRZ3BMeUJMRWJyYXRaUlE9PSIsInZhbHVlIjoiYng4d1N0OUt0M0NoaktEM2x6alVldjR2Ui9HUVZ0elE2cVA0Nlpwd2pCUVdOaTdoTTlFTUtDYWJGRjZKbHBYSEYrQWMveElYZms1SG0rQ3p0bnRFdUd1MDZRQStNTE9JbHpZbnMzazVlY0VqZStNSElpZ3hJa09FVXNVamVmSWEiLCJtYWMiOiI2NmUyYjhhMzlhYWI3NzI2ODdkZmFjMDkyNThjMzA4MjRmMThlOTAxMGQ1YzBlY2I2NzExNjdmYTZlNDVhMWIzIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:39:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhhclJ0UVBlTHR2RWtxTWhpWmREWVE9PSIsInZhbHVlIjoiVkNDRGRLekpYOHpjakJyZ2JXQXRTcFV1Tm1jd21qUDNoTjBNUnlleklma0JjMGpCS1puM21nbUVxYm11bklzMWVCMENWMDRUWWVkWi9BRCsyK0hRZjc1Nk8rUTIzMGc4UjZRM203Nm1wOGlqbTVPS0FRQU5VT0RDaXExT3FuNlUiLCJtYWMiOiIzZDA0YWU1Y2U3MzMzNDY0NGQxMGNjYTFkZjljYzI5NmJkNzllNWYxYjlmN2Q2YWQ3ZjgwMWY3ZjdlZTlhYTBkIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:39:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImkvYTVtNnhRZ3BMeUJMRWJyYXRaUlE9PSIsInZhbHVlIjoiYng4d1N0OUt0M0NoaktEM2x6alVldjR2Ui9HUVZ0elE2cVA0Nlpwd2pCUVdOaTdoTTlFTUtDYWJGRjZKbHBYSEYrQWMveElYZms1SG0rQ3p0bnRFdUd1MDZRQStNTE9JbHpZbnMzazVlY0VqZStNSElpZ3hJa09FVXNVamVmSWEiLCJtYWMiOiI2NmUyYjhhMzlhYWI3NzI2ODdkZmFjMDkyNThjMzA4MjRmMThlOTAxMGQ1YzBlY2I2NzExNjdmYTZlNDVhMWIzIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:39:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1520091952 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C09JbWiNSYuZ6fSTKKrlqIbJ1ZqyZwDMTg40i3qQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://sikapal.test/stok-sparepart/export-form</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520091952\", {\"maxDepth\":0})</script>\n"}}