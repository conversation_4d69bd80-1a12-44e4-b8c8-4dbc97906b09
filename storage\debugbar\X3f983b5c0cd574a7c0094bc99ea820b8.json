{"__meta": {"id": "X3f983b5c0cd574a7c0094bc99ea820b8", "datetime": "2025-07-20 07:17:08", "utime": **********.665777, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.419777, "end": **********.665798, "duration": 0.24602103233337402, "duration_str": "246ms", "measures": [{"label": "Booting", "start": **********.419777, "relative_start": 0, "end": **********.605952, "relative_end": **********.605952, "duration": 0.18617510795593262, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.605967, "relative_start": 0.18619012832641602, "end": **********.6658, "relative_end": 2.1457672119140625e-06, "duration": 0.05983304977416992, "duration_str": "59.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24760144, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET dashboard", "middleware": "web, auth, admin.kapal", "controller": "App\\Http\\Controllers\\DashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=15\" onclick=\"\">app/Http/Controllers/DashboardController.php:15-180</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00252, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.647053, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.651125, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-410765843 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-410765843\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1648165217 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1648165217\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1207630599 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1207630599\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1222730601 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">http://sikapal.test/pembelian-kantor?page=1&amp;search=PRESSURE+TRANSMITER%09</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlJEa2x4eXlYOFJqem9FTG5JTlVieVE9PSIsInZhbHVlIjoiYTVsUGF2L3JpRVBJbWlrZzBkUXJZODhnVkhKNTFsOUUvempWbmxLdHBwaGhQSU5VVnlSRi9iN0dpWitJNlJwT1gvdmdyanV6VUhRS0VnaDVjTE1ydWNWUmtzNlhIbVFKWUNnMmRRcncxSlI5VFovUFMyR1BPdnBPekdXNlFLY2QiLCJtYWMiOiI1YjhiYmUyNDBlZWJkNjg5YzYxNGY5ODE5YjZjYzczYjFmYjgwMWVlM2NhNTFjNWNkMDk1N2E2MGQ4ZDUzMTRjIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImtzNlJSY1ZIeEpaMzkraFhreWhYSmc9PSIsInZhbHVlIjoiMTcwcXhVOVl6bGl5Y2hXNHUwUFJCY2VrajN5eXNaOVdDSmUreS9nYjhWTFlVRjZHUnZwcnQwYk1IK2kzT3pjYVZ3NVl5b3hyaWk2M1pBQlB1NjNmMVp1WjNMSlduOENNR2hGMERYV016S1ZTRjkyVjIwZ3dVaDlvU3FtNE9CWEgiLCJtYWMiOiJlYTBlOGNlZDljNzVhZDc4MzdiNmYwMjE4MjU1NGE1ZTgzOWRjNmUzMzAwYTc4MTgyNzNlMmZjNmU0MTM3YjNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222730601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1850621326 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850621326\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1176256219 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:17:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlSZEpvWmxEa0NkRHV5YVJsd1NrM2c9PSIsInZhbHVlIjoiOHZ2ODRUUHByeWJTOGlMeVhDOUQ5WWpqeHhlVGgwRHpCU1ZFQnl2STN2QjU5ckVXZmpMYjI1TTlDY0ZKTXZqRDJzSzI2bHk0NW5BcmpJU3oyUjdEN0Vta2RRRkpia3g4MEdRNVZzUERYMUZYTVZudE9zcS9vUGFWMW9qTnhzdGkiLCJtYWMiOiJmNjI2OGJmNzBlNTFjYjViMzA2NzY5NWUzMmY1ZGVmOGVkZjRhNTY4OWJmMzYyMmU5ZDEyNTVhZDY5OGY3NThlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:17:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IlVtQW0vNHZXOXF2NzcrQ2JwY0FZQkE9PSIsInZhbHVlIjoiSDRoQnlBRHRtQnFIakVwVDBlM2MxRGpYaG4rcCtTV0pqVGQ0anBhNmlsVndSV3lXTzU5ZVc5ZldtS204V0EyTDhZUzJ2SFdyMlo4NVJFZFZBUlhpK3JGUDFQSjNpRGNDRDNIYXU0b25OZU9WWVRSYk82bDNRWWtOYjNMcG9LRWciLCJtYWMiOiIyZjg5NjJhNjcwMDViNzllNjBhMDQ2MWZjMjY1YmRkNjkyZjdiNmQ5YWQ5ZDc0MjgxZGJhZTEzMjlmMThiZjIyIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:17:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlSZEpvWmxEa0NkRHV5YVJsd1NrM2c9PSIsInZhbHVlIjoiOHZ2ODRUUHByeWJTOGlMeVhDOUQ5WWpqeHhlVGgwRHpCU1ZFQnl2STN2QjU5ckVXZmpMYjI1TTlDY0ZKTXZqRDJzSzI2bHk0NW5BcmpJU3oyUjdEN0Vta2RRRkpia3g4MEdRNVZzUERYMUZYTVZudE9zcS9vUGFWMW9qTnhzdGkiLCJtYWMiOiJmNjI2OGJmNzBlNTFjYjViMzA2NzY5NWUzMmY1ZGVmOGVkZjRhNTY4OWJmMzYyMmU5ZDEyNTVhZDY5OGY3NThlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:17:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IlVtQW0vNHZXOXF2NzcrQ2JwY0FZQkE9PSIsInZhbHVlIjoiSDRoQnlBRHRtQnFIakVwVDBlM2MxRGpYaG4rcCtTV0pqVGQ0anBhNmlsVndSV3lXTzU5ZVc5ZldtS204V0EyTDhZUzJ2SFdyMlo4NVJFZFZBUlhpK3JGUDFQSjNpRGNDRDNIYXU0b25OZU9WWVRSYk82bDNRWWtOYjNMcG9LRWciLCJtYWMiOiIyZjg5NjJhNjcwMDViNzllNjBhMDQ2MWZjMjY1YmRkNjkyZjdiNmQ5YWQ5ZDc0MjgxZGJhZTEzMjlmMThiZjIyIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:17:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176256219\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1216212068 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://sikapal.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216212068\", {\"maxDepth\":0})</script>\n"}}