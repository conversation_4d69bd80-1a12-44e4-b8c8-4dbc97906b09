@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card modern-card">
                <div class="card-header">
                    @if(auth()->user()->isAdminKapal())
                        <h3 class="card-title">Export Data Sparepart dan <PERSON> yang Ada di Kapal</h3>
                    @else
                        <h3 class="card-title">Export Data Sparepart dan <PERSON></h3>
                    @endif
                </div>
                <div class="card-body">
                    <form action="{{ route('stok-sparepart.export') }}" method="GET" id="exportForm">
                        <div class="form-group mb-3">
                            <label for="kapal_id">Pilih <PERSON>pal</label>
                            <select name="kapal_id" id="kapal_id" class="form-control" required>
                                @foreach($kapals as $kapal)
                                    @if($kapal->jenis == 'Tugboat')
                                        <option value="{{ $kapal->id }}" {{ $kapal->nama == 'TB. GEMA 201' ? 'selected' : '' }}>{{ $kapal->nama }}</option>
                                    @endif
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="date_range" class="form-label">Rentang Tanggal</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white">
                                    <i class="fas fa-calendar"></i>
                                </span>
                                <input type="text" 
                                       class="form-control" 
                                       id="date_range" 
                                       name="date_range" 
                                       placeholder="Pilih Rentang Tanggal" 
                                       required 
                                       readonly>
                                <input type="hidden" name="tanggal_awal" id="tanggal_awal">
                                <input type="hidden" name="tanggal_akhir" id="tanggal_akhir">
                            </div>
                            <div class="form-text">Pilih rentang tanggal untuk data yang akan diexport</div>
                        </div>
                        <input type="hidden" name="type" id="exportType" value="excel">
                        <div class="mt-3">
                            <button type="button" class="btn btn-success" onclick="submitForm('excel')">
                                <i class="fas fa-file-excel me-2"></i>Export Excel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="submitForm('pdf')">
                                <i class="fas fa-file-pdf me-2"></i>Export PDF
                            </button>
                            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<style>
/* Style untuk Flatpickr Calendar */
.flatpickr-calendar {
    background: #fff;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.flatpickr-months {
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
    border-radius: 8px 8px 0 0;
    padding: 8px 0;
}

.flatpickr-months .flatpickr-month {
    color: #fff !important;
    fill: #fff !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    color: #fff !important;
    fill: #fff !important;
    padding: 8px;
}

.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
    color: #e2e8f0 !important;
    fill: #e2e8f0 !important;
}

.flatpickr-current-month {
    padding: 8px 0;
    color: #fff !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    color: #fff !important;
    background: transparent !important;
}

.flatpickr-current-month input.cur-year {
    color: #fff !important;
}

.flatpickr-weekdays {
    background: #f8fafc;
}

.flatpickr-weekday {
    background: #f8fafc;
    color: #0ea5e9 !important;
    font-weight: 600;
}

.flatpickr-day {
    border-radius: 6px;
    margin: 2px;
    color: #1e293b;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange {
    background: #0ea5e9 !important;
    border-color: #0ea5e9 !important;
    color: #fff !important;
}

.flatpickr-day.inRange {
    background: #e0f2fe !important;
    border-color: #e0f2fe !important;
    color: #0ea5e9 !important;
    box-shadow: none !important;
}

.flatpickr-day:hover {
    background: #f0f9ff !important;
    border-color: #f0f9ff !important;
}

.flatpickr-day.today {
    border-color: #0ea5e9;
    color: #0ea5e9 !important;
}

.flatpickr-day.today:hover {
    background: #0ea5e9 !important;
    border-color: #0ea5e9 !important;
    color: #fff !important;
}

/* Style untuk card */
.modern-card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.modern-card .card-header {
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1rem 1.5rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/id.js"></script>

<script>
$(document).ready(function() {
    // Inisialisasi date range picker
    flatpickr("#date_range", {
        mode: "range",
        dateFormat: "d-m-Y",
        locale: "id",
        rangeSeparator: " sampai ",
        required: true,
        allowInput: false,
        disableMobile: true,
        onChange: function(selectedDates, dateStr, instance) {
            if (selectedDates.length === 2) {
                const startDate = selectedDates[0];
                const endDate = selectedDates[1];
                
                const formatDate = (date) => {
                    const d = new Date(date);
                    const day = String(d.getDate()).padStart(2, '0');
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const year = d.getFullYear();
                    return `${day}-${month}-${year}`;
                };
                
                // Set value untuk display
                instance.input.value = `${formatDate(startDate)} sampai ${formatDate(endDate)}`;
                
                // Set value untuk hidden input dalam format YYYY-MM-DD
                const formatServerDate = (date) => {
                    const d = new Date(date);
                    const year = d.getFullYear();
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };
                
                $('#tanggal_awal').val(formatServerDate(startDate));
                $('#tanggal_akhir').val(formatServerDate(endDate));
            }
        }
    });
});

function submitForm(type) {
    const dateRange = $('#date_range').val();
    if (!dateRange || dateRange.trim() === '') {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Silakan pilih tanggal terlebih dahulu'
        });
        return false;
    }

    // Cek apakah tanggal sudah diset
    const tanggalAwal = $('#tanggal_awal').val();
    const tanggalAkhir = $('#tanggal_akhir').val();
    
    if (!tanggalAwal || !tanggalAkhir) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Format tanggal tidak valid'
        });
        return false;
    }

    // Set type dan submit form
    $('#exportType').val(type);
    $('#exportForm').submit();
}
</script>
@endpush

@endsection 