<?php
use App\Helpers\HashIdHelper;
?>

<?php $__env->startSection('content'); ?>
<!-- Tambahkan meta tag CSRF -->
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

<style>
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Ensure SweetAlert appears above Bootstrap modals */
.swal-high-z-index {
    z-index: 9999 !important;
}

.swal2-container.swal-high-z-index {
    z-index: 9999 !important;
}

.info-group {
    transition: all 0.3s ease;
}

.info-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
}

.table {
    vertical-align: middle;
}

.bg-gradient {
    background: linear-gradient(45deg, #1a237e, #283593);
}

.modal-content {
    border: none;
    border-radius: 15px;
    position: relative;
    z-index: 1051;
}

.modal-header {
    border-radius: 15px 15px 0 0;
}

/* Fix modal backdrop issues - Use extremely high z-index values */
.modal-backdrop {
    z-index: 999998 !important;
}

.modal {
    z-index: 999999 !important;
    position: fixed !important;
}

.modal-dialog {
    pointer-events: auto !important;
    z-index: 1000000 !important;
    position: relative !important;
}

.modal-content {
    z-index: 1000001 !important;
    position: relative !important;
}

/* Ensure modal content is clickable */
.modal.show {
    display: block !important;
    z-index: 999999 !important;
}

.modal.show .modal-dialog {
    pointer-events: auto !important;
    z-index: 1000000 !important;
}

.modal.show .modal-content {
    pointer-events: auto !important;
    z-index: 1000001 !important;
}

/* Specific styling for pemeriksaan modals to ensure they're on absolute top */
[id^="modalPemeriksaan"] {
    z-index: 2147483647 !important; /* Maximum possible z-index */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
}

[id^="modalPemeriksaan"].show {
    z-index: 2147483647 !important;
    display: block !important;
}

[id^="modalPemeriksaan"] .modal-dialog {
    z-index: 2147483647 !important;
    position: relative !important;
    pointer-events: auto !important;
}

[id^="modalPemeriksaan"] .modal-content {
    z-index: 2147483647 !important;
    position: relative !important;
    pointer-events: auto !important;
}

/* Override any competing z-index values */
.modal-backdrop.show {
    z-index: 2147483646 !important; /* One less than maximum for modal backdrop */
}

/* Ensure no other elements can appear above modal */
* {
    position: relative;
}

[id^="modalPemeriksaan"] * {
    z-index: inherit !important;
}

/* URGENT FIX: Custom modal system CSS */
.custom-modal-system {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 2147483647 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    pointer-events: auto !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    overflow-y: auto !important;
}

.custom-modal-system * {
    pointer-events: auto !important;
    z-index: inherit !important;
}

.custom-modal-system .modal-dialog {
    z-index: 2147483647 !important;
    position: relative !important;
    pointer-events: auto !important;
    margin: auto !important;
    max-width: 500px !important;
    width: 100% !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    background: white !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
}

.custom-modal-system .modal-content {
    z-index: 2147483647 !important;
    position: relative !important;
    pointer-events: auto !important;
    border: none !important;
    border-radius: 15px !important;
    box-shadow: none !important;
    background: white !important;
    width: 100% !important;
}

.custom-modal-system .modal-header {
    border-bottom: 1px solid #dee2e6 !important;
    padding: 1rem !important;
    border-radius: 15px 15px 0 0 !important;
}

.custom-modal-system .modal-body {
    padding: 1rem !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.custom-modal-system .modal-footer {
    border-top: 1px solid #dee2e6 !important;
    padding: 1rem !important;
    border-radius: 0 0 15px 15px !important;
}

.custom-modal-system .btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    margin: 2px !important;
}

.custom-modal-system input,
.custom-modal-system textarea,
.custom-modal-system select,
.custom-modal-system button {
    pointer-events: auto !important;
}

.custom-modal-system .form-control {
    width: 100% !important;
    margin-bottom: 10px !important;
}

.custom-modal-system .btn-kondisi {
    min-width: 80px !important;
    margin: 5px !important;
}

/* Responsive modal */
@media (max-width: 576px) {
    .custom-modal-system .modal-dialog {
        max-width: 95% !important;
        margin: 10px auto !important;
    }

    .custom-modal-system {
        padding: 10px !important;
    }
}

/* Ensure modal is always visible */
.custom-modal-system .modal-dialog {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.custom-modal-system .modal-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Hide all Bootstrap modal backdrops */
.modal-backdrop {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Prevent body scroll lock */
body.modal-open {
    overflow: auto !important;
    padding-right: 0 !important;
}

.card {
    border-radius: 15px;
    overflow: hidden;
}

.table thead th {
    background: rgba(0,0,0,.03);
    font-weight: 600;
}

/* Tambahan CSS untuk sorting */
.sortable {
    cursor: pointer;
    position: relative;
}

.sortable i {
    font-size: 0.8rem;
    opacity: 0.5;
    transition: all 0.2s;
}

.sortable:hover i {
    opacity: 1;
}

.sortable.asc i:before {
    content: "\f0de"; /* Font Awesome sort-up icon */
}

.sortable.desc i:before {
    content: "\f0dd"; /* Font Awesome sort-down icon */
}

/* CSS untuk pagination */
.pagination {
    margin: 0;
}

.page-link {
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    margin: 0 0.25rem;
}

.page-item.active .page-link {
    background-color: #1a237e;
    border-color: #1a237e;
}
</style>

<div class="container-fluid px-4 py-4">
    <!-- Tombol Update Perjalanan Kapal -->
    <div class="row mb-4">
        <div class="col-12">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updatePerjalananModal">
                <i class="fas fa-ship me-2"></i>Update Perjalanan Kapal
            </button>
        </div>
    </div>

    <!-- Modal Update Perjalanan -->
    <div class="modal fade" id="updatePerjalananModal" tabindex="-1" aria-labelledby="updatePerjalananModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="updatePerjalananModalLabel">
                        <i class="fas fa-ship me-2"></i>Update Perjalanan Kapal
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Identitas Kapal -->
                    <div class="card mb-4 shadow-sm border-0">
                        <div class="card-header bg-gradient text-white" style="background-color: #1a237e;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-ship fa-2x me-2"></i>
                                <h5 class="mb-0">Identitas Kapal</h5>
                            </div>
                        </div>
                        <div class="card-body bg-light">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="info-group p-3 bg-white rounded shadow-sm">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon-circle bg-primary text-white me-3">
                                                <i class="fas fa-ship"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Nama Kapal</small>
                                                <strong><?php echo e($kapal->nama); ?></strong>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon-circle bg-info text-white me-3">
                                                <i class="fas fa-tag"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Jenis Kapal</small>
                                                <strong><?php echo e($kapal->jenis_kapal->nama); ?></strong>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-circle bg-success text-white me-3">
                                                <i class="fas fa-broadcast-tower"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Call Sign</small>
                                                <strong><?php echo e($kapal->call_sign); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group p-3 bg-white rounded shadow-sm">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon-circle bg-warning text-white me-3">
                                                <i class="fas fa-weight"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">GT/NT</small>
                                                <strong><?php echo e($kapal->gt_nt); ?></strong>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="icon-circle bg-danger text-white me-3">
                                                <i class="fas fa-anchor"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">Port Registry</small>
                                                <strong><?php echo e($kapal->port_of_registry); ?></strong>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="icon-circle bg-secondary text-white me-3">
                                                <i class="fas fa-fingerprint"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">IMO Number</small>
                                                <strong><?php echo e($kapal->imo); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Running Hours Info -->
                                <div class="col-12">
                                    <div class="info-group p-3 bg-white rounded shadow-sm">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-primary text-white me-3">
                                                        <i class="fas fa-clock"></i>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted d-block">Running Hours ME PS</small>
                                                        <strong><?php echo e(number_format($kapal->rh_me_ps)); ?> jam</strong>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-info text-white me-3">
                                                        <i class="fas fa-clock"></i>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted d-block">Running Hours ME SB</small>
                                                        <strong><?php echo e(number_format($kapal->rh_me_sb)); ?> jam</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Histori Perjalanan -->
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-gradient text-white d-flex justify-content-between align-items-center" style="background-color: #2e7d32;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-history fa-2x me-2"></i>
                                <h5 class="mb-0">Histori Perjalanan</h5>
                            </div>
                            <button type="button" class="btn btn-light btn-sm d-flex align-items-center" onclick="showFormTambah()">
                                <i class="fas fa-plus me-2"></i>
                                Tambah Perjalanan
                            </button>
                        </div>
                        <div class="card-body bg-light">
                            <div class="table-responsive">
                                <table class="table table-hover bg-white rounded shadow-sm" id="historiPerjalananTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="text-center">#</th>
                                            <th class="sortable" data-sort="tanggal">Tanggal <i class="fas fa-sort ms-1"></i></th>
                                            <th class="text-center sortable" data-sort="rh_me_ps">RH ME PS <i class="fas fa-sort ms-1"></i></th>
                                            <th class="text-center sortable" data-sort="rh_me_sb">RH ME SB <i class="fas fa-sort ms-1"></i></th>
                                            <th class="sortable" data-sort="tujuan">Tujuan <i class="fas fa-sort ms-1"></i></th>
                                            <th class="text-center">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody id="historiPerjalananBody">
                                        <?php $__currentLoopData = $kapal->historiPerjalanan()->orderBy('created_at', 'asc')->paginate(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $histori): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="text-center"><?php echo e($index + 1); ?></td>
                                            <td><?php echo e(\Carbon\Carbon::parse($histori->created_at)->format('d/m/Y H:i')); ?></td>
                                            <td class="text-center"><?php echo e(number_format($histori->rh_me_ps)); ?></td>
                                            <td class="text-center"><?php echo e(number_format($histori->rh_me_sb)); ?></td>
                                            <td><?php echo e($histori->tujuan); ?></td>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-sm btn-danger btn-delete-histori" data-id="<?php echo e($histori->id); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div class="text-muted">
                                        Menampilkan <?php echo e($kapal->historiPerjalanan()->count()); ?> data
                                    </div>
                                    <div>
                                        <?php echo e($kapal->historiPerjalanan()->orderBy('created_at', 'asc')->paginate(10)->links()); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Tambah Perjalanan -->
                    <div class="card" id="formTambahPerjalanan" style="display: none;">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">Tambah Perjalanan Baru</h6>
                        </div>
                        <div class="card-body">
                            <form id="formUpdatePerjalanan" action="<?php echo e(route('checklist-engine.update-perjalanan', ['kapal' => $kapal->hash_id])); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="rh_me_ps" class="form-label">Running Hours ME Portside</label>
                                            <input type="number" class="form-control" id="rh_me_ps" name="rh_me_ps" value="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="rh_me_sb" class="form-label">Running Hours ME Starboardside</label>
                                            <input type="number" class="form-control" id="rh_me_sb" name="rh_me_sb" value="0" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="tujuan" class="form-label">Tujuan Kapal</label>
                                    <input type="text" class="form-control" id="tujuan" name="tujuan" required>
                                </div>
                                <div class="text-end">
                                    <button type="button" class="btn btn-secondary" onclick="hideFormTambah()">Batal</button>
                                    <button type="submit" class="btn btn-primary">Simpan Perjalanan</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Notifikasi Checklist Rutin -->
        <?php
            $notifRutin = collect($notifications)->filter(function($notif) {
                return isset($notif['interval']) && $notif['interval'] !== null;
            });
        ?>
        <?php if($notifRutin->count() > 0): ?>
        <div class="col-12 mb-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Notifikasi Checklist Engine Rutin
                        <span class="badge bg-danger ms-2"><?php echo e($notifRutin->count()); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="notificationAccordionRutin">
                        <!-- PORTSIDE Notifications -->
                        <?php
                            $portsideNotifications = $notifRutin->where('engine', 'PORTSIDE');
                        ?>
                        <?php if($portsideNotifications->count() > 0): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePortsideRutin">
                                    <i class="fas fa-cogs me-2"></i>
                                    Main Engine Portside
                                    <span class="badge bg-danger ms-2"><?php echo e($portsideNotifications->count()); ?></span>
                                </button>
                            </h2>
                            <div id="collapsePortsideRutin" class="accordion-collapse collapse" data-bs-parent="#notificationAccordionRutin">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Item Pemeriksaan</th>
                                                    <th>Sistem</th>
                                                    <th>Interval</th>
                                                    <th>Running Hours</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $counter = 1; ?>
                                                <?php $__currentLoopData = $portsideNotifications->sortBy('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($counter++); ?></td>
                                                    <td><?php echo e($notif['item_pemeriksaan']); ?></td>
                                                    <td><?php echo e($notif['sistem'] ?? '-'); ?></td>
                                                    <td><?php echo e($notif['waktu']); ?></td>
                                                    <td><?php echo e(number_format($notif['running_hours'])); ?> jam</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                                            <i class="fas fa-check"></i> Cek
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal Pemeriksaan -->
                                                <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Pemeriksaan Item</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <form id="formPemeriksaan<?php echo e($notif['id']); ?>" action="<?php echo e(route('checklist-engine.store-item')); ?>" method="POST">
                                                                <?php echo csrf_field(); ?>
                                                                <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                                                <input type="hidden" name="engine" value="PORTSIDE">
                                                                <input type="hidden" name="kapal_id" value="<?php echo e($kapal->hash_id); ?>">
                                                                
                                                                <div class="modal-body">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Nama Pemeriksaan</label>
                                                                        <input type="text" class="form-control" value="<?php echo e($notif['item_pemeriksaan']); ?>" readonly>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Kondisi</label>
                                                                        <div class="d-flex gap-2">
                                                                            <input type="hidden" name="kondisi" id="kondisi<?php echo e($notif['id']); ?>" required>
                                                                            <button type="button" class="btn btn-outline-success flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="bagus"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-check-circle me-2"></i>Bagus
                                                                            </button>
                                                                            <button type="button" class="btn btn-outline-danger flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="tidak"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-times-circle me-2"></i>Tidak Bagus
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Keterangan</label>
                                                                        <textarea class="form-control" name="keterangan" rows="3"></textarea>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Tanggal Pemeriksaan</label>
                                                                        <input type="datetime-local" class="form-control" name="tanggal_pemeriksaan" 
                                                                            value="<?php echo e(now()->format('Y-m-d\TH:i')); ?>" required>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- STARBOARDSIDE Notifications -->
                        <?php
                            $starboardNotifications = $notifRutin->where('engine', 'STARBOARDSIDE');
                        ?>
                        <?php if($starboardNotifications->count() > 0): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseStarboardRutin">
                                    <i class="fas fa-cogs me-2"></i>
                                    Main Engine Starboardside
                                    <span class="badge bg-danger ms-2"><?php echo e($starboardNotifications->count()); ?></span>
                                </button>
                            </h2>
                            <div id="collapseStarboardRutin" class="accordion-collapse collapse" data-bs-parent="#notificationAccordionRutin">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Item Pemeriksaan</th>
                                                    <th>Sistem</th>
                                                    <th>Interval</th>
                                                    <th>Running Hours</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $counter = 1; ?>
                                                <?php $__currentLoopData = $starboardNotifications->sortBy('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($counter++); ?></td>
                                                    <td><?php echo e($notif['item_pemeriksaan']); ?></td>
                                                    <td><?php echo e($notif['sistem'] ?? '-'); ?></td>
                                                    <td><?php echo e($notif['waktu']); ?></td>
                                                    <td><?php echo e(number_format($notif['running_hours'])); ?> jam</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                                            <i class="fas fa-check"></i> Cek
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal Pemeriksaan -->
                                                <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Pemeriksaan Item</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <form id="formPemeriksaan<?php echo e($notif['id']); ?>" action="<?php echo e(route('checklist-engine.store-item')); ?>" method="POST">
                                                                <?php echo csrf_field(); ?>
                                                                <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                                                <input type="hidden" name="engine" value="STARBOARDSIDE">
                                                                <input type="hidden" name="kapal_id" value="<?php echo e($kapal->hash_id); ?>">
                                                                
                                                                <div class="modal-body">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Nama Pemeriksaan</label>
                                                                        <input type="text" class="form-control" value="<?php echo e($notif['item_pemeriksaan']); ?>" readonly>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Kondisi</label>
                                                                        <div class="d-flex gap-2">
                                                                            <input type="hidden" name="kondisi" id="kondisi<?php echo e($notif['id']); ?>" required>
                                                                            <button type="button" class="btn btn-outline-success flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="bagus"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-check-circle me-2"></i>Bagus
                                                                            </button>
                                                                            <button type="button" class="btn btn-outline-danger flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="tidak"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-times-circle me-2"></i>Tidak Bagus
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Keterangan</label>
                                                                        <textarea class="form-control" name="keterangan" rows="3"></textarea>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Tanggal Pemeriksaan</label>
                                                                        <input type="datetime-local" class="form-control" name="tanggal_pemeriksaan" 
                                                                            value="<?php echo e(now()->format('Y-m-d\TH:i')); ?>" required>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- OTHER SYSTEMS (RUTIN) -->
                        <?php
                            $otherRutinNotifications = $notifRutin->where('engine', 'OTHER')->groupBy('jenis');
                        ?>
                        <?php $__currentLoopData = $otherRutinNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jenis => $items): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOtherRutin<?php echo e($loop->index); ?>">
                                    <i class="fas fa-tools me-2"></i>
                                    <?php echo e($jenis); ?>

                                    <span class="badge bg-danger ms-2"><?php echo e($items->count()); ?></span>
                                </button>
                            </h2>
                            <div id="collapseOtherRutin<?php echo e($loop->index); ?>" class="accordion-collapse collapse" data-bs-parent="#notificationAccordionRutin">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Item Pemeriksaan</th>
                                                    <th>Sistem</th>
                                                    <th>Running Hours</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $counter = 1; ?>
                                                <?php $__currentLoopData = $items->sortBy('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($counter++); ?></td>
                                                    <td><?php echo e($notif['item_pemeriksaan']); ?></td>
                                                    <td><?php echo e($notif['sistem'] ?? '-'); ?></td>
                                                    <td><?php echo e(number_format($notif['running_hours'])); ?> jam</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                                            <i class="fas fa-check"></i> Cek
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal untuk setiap item -->
                                                <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Pemeriksaan: <?php echo e($notif['item_pemeriksaan']); ?></h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                            </div>
                                                            <form action="<?php echo e(route('checklist-engine.store-result')); ?>" method="POST">
                                                                <?php echo csrf_field(); ?>
                                                                <div class="modal-body">
                                                                    <input type="hidden" name="kapal_id" value="<?php echo e($kapal->id); ?>">
                                                                    <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                                                    <input type="hidden" name="history_id" value="<?php echo e($currentHistory->id ?? ''); ?>">

                                                                    <div class="mb-3">
                                                                        <label class="form-label">Kondisi</label>
                                                                        <select name="kondisi" class="form-select" required>
                                                                            <option value="">Pilih Kondisi</option>
                                                                            <option value="bagus">Bagus</option>
                                                                            <option value="rusak">Rusak</option>
                                                                        </select>
                                                                    </div>

                                                                    <div class="mb-3">
                                                                        <label class="form-label">Keterangan</label>
                                                                        <textarea name="keterangan" class="form-control" rows="3"></textarea>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Notifikasi Checklist Situasional -->
        <?php
            $notifSituasional = collect($notifications)->filter(function($notif) {
                return !isset($notif['interval']) || $notif['interval'] === null;
            });
        ?>
        <?php if($notifSituasional->count() > 0): ?>
        <div class="col-12 mb-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        Notifikasi Checklist Engine Situasional
                        <span class="badge bg-danger ms-2"><?php echo e($notifSituasional->count()); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="notificationAccordionSituasional">
                        <!-- MAIN ENGINE PORTSIDE -->
                        <?php
                            $portsideNotifications = $notifSituasional->where('engine', 'PORTSIDE');
                        ?>
                        <?php if($portsideNotifications->count() > 0): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePortsideSituasional">
                                    <i class="fas fa-cogs me-2"></i>
                                    MAIN ENGINE PORTSIDE (MITSUBISHI S6R2-MTKII/ S6R2-MPTK3)
                                    <span class="badge bg-danger ms-2"><?php echo e($portsideNotifications->count()); ?></span>
                                </button>
                            </h2>
                            <div id="collapsePortsideSituasional" class="accordion-collapse collapse" data-bs-parent="#notificationAccordionSituasional">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Item Pemeriksaan</th>
                                                    <th>Sistem</th>
                                                    <th>Running Hours</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $counter = 1; ?>
                                                <?php $__currentLoopData = $portsideNotifications->sortBy('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($counter++); ?></td>
                                                    <td><?php echo e($notif['item_pemeriksaan']); ?></td>
                                                    <td><?php echo e($notif['sistem'] ?? '-'); ?></td>
                                                    <td><?php echo e(number_format($notif['running_hours'])); ?> jam</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                                            <i class="fas fa-check"></i> Cek
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal Pemeriksaan -->
                                                <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Pemeriksaan Item</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <form id="formPemeriksaan<?php echo e($notif['id']); ?>" action="<?php echo e(route('checklist-engine.store-item')); ?>" method="POST">
                                                                <?php echo csrf_field(); ?>
                                                                <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                                                <input type="hidden" name="engine" value="PORTSIDE">
                                                                <input type="hidden" name="kapal_id" value="<?php echo e($kapal->hash_id); ?>">
                                                                
                                                                <div class="modal-body">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Nama Pemeriksaan</label>
                                                                        <input type="text" class="form-control" value="<?php echo e($notif['item_pemeriksaan']); ?>" readonly>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Kondisi</label>
                                                                        <div class="d-flex gap-2">
                                                                            <input type="hidden" name="kondisi" id="kondisi<?php echo e($notif['id']); ?>" required>
                                                                            <button type="button" class="btn btn-outline-success flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="bagus"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-check-circle me-2"></i>Bagus
                                                                            </button>
                                                                            <button type="button" class="btn btn-outline-danger flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="tidak"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-times-circle me-2"></i>Tidak Bagus
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Keterangan</label>
                                                                        <textarea class="form-control" name="keterangan" rows="3"></textarea>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Tanggal Pemeriksaan</label>
                                                                        <input type="datetime-local" class="form-control" name="tanggal_pemeriksaan" 
                                                                            value="<?php echo e(now()->format('Y-m-d\TH:i')); ?>" required>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- MAIN ENGINE STARBOARDSIDE -->
                        <?php
                            $starboardNotifications = $notifSituasional->where('engine', 'STARBOARDSIDE');
                        ?>
                        <?php if($starboardNotifications->count() > 0): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseStarboardSituasional">
                                    <i class="fas fa-cogs me-2"></i>
                                    MAIN ENGINE STARBOARDSIDE (MITSUBISHI S6R2-MTKII/ S6R2-MPTK3)
                                    <span class="badge bg-danger ms-2"><?php echo e($starboardNotifications->count()); ?></span>
                                </button>
                            </h2>
                            <div id="collapseStarboardSituasional" class="accordion-collapse collapse" data-bs-parent="#notificationAccordionSituasional">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Item Pemeriksaan</th>
                                                    <th>Sistem</th>
                                                    <th>Running Hours</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $counter = 1; ?>
                                                <?php $__currentLoopData = $starboardNotifications->sortBy('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($counter++); ?></td>
                                                    <td><?php echo e($notif['item_pemeriksaan']); ?></td>
                                                    <td><?php echo e($notif['sistem'] ?? '-'); ?></td>
                                                    <td><?php echo e(number_format($notif['running_hours'])); ?> jam</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                                            <i class="fas fa-check"></i> Cek
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal Pemeriksaan -->
                                                <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Pemeriksaan Item</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <form id="formPemeriksaan<?php echo e($notif['id']); ?>" action="<?php echo e(route('checklist-engine.store-item')); ?>" method="POST">
                                                                <?php echo csrf_field(); ?>
                                                                <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                                                <input type="hidden" name="engine" value="STARBOARDSIDE">
                                                                <input type="hidden" name="kapal_id" value="<?php echo e($kapal->hash_id); ?>">
                                                                
                                                                <div class="modal-body">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Nama Pemeriksaan</label>
                                                                        <input type="text" class="form-control" value="<?php echo e($notif['item_pemeriksaan']); ?>" readonly>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Kondisi</label>
                                                                        <div class="d-flex gap-2">
                                                                            <input type="hidden" name="kondisi" id="kondisi<?php echo e($notif['id']); ?>" required>
                                                                            <button type="button" class="btn btn-outline-success flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="bagus"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-check-circle me-2"></i>Bagus
                                                                            </button>
                                                                            <button type="button" class="btn btn-outline-danger flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="tidak"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-times-circle me-2"></i>Tidak Bagus
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Keterangan</label>
                                                                        <textarea class="form-control" name="keterangan" rows="3"></textarea>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Tanggal Pemeriksaan</label>
                                                                        <input type="datetime-local" class="form-control" name="tanggal_pemeriksaan" 
                                                                            value="<?php echo e(now()->format('Y-m-d\TH:i')); ?>" required>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- OTHER SYSTEMS -->
                        <?php
                            $otherNotifications = $notifSituasional->where('engine', 'OTHER')->groupBy('jenis');
                        ?>
                        <?php $__currentLoopData = $otherNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jenis => $items): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse<?php echo e(preg_replace('/[^a-zA-Z0-9]/', '', $jenis)); ?>">
                                    <i class="fas fa-cogs me-2"></i>
                                    <?php echo e($jenis); ?>

                                    <span class="badge bg-danger ms-2"><?php echo e($items->count()); ?></span>
                                </button>
                            </h2>
                            <div id="collapse<?php echo e(preg_replace('/[^a-zA-Z0-9]/', '', $jenis)); ?>" 
                                 class="accordion-collapse collapse" 
                                 data-bs-parent="#notificationAccordionSituasional">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>Item Pemeriksaan</th>
                                                    <th>Sistem</th>
                                                    <th>Running Hours</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $counter = 1; ?>
                                                <?php $__currentLoopData = $items->sortBy('id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($counter++); ?></td>
                                                    <td><?php echo e($notif['item_pemeriksaan']); ?></td>
                                                    <td><?php echo e($notif['sistem'] ?? '-'); ?></td>
                                                    <td><?php echo e(number_format($notif['running_hours'])); ?> jam</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                                            <i class="fas fa-check"></i> Cek
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Modal Pemeriksaan -->
                                                <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Pemeriksaan Item</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <form id="formPemeriksaan<?php echo e($notif['id']); ?>" action="<?php echo e(route('checklist-engine.store-item')); ?>" method="POST">
                                                                <?php echo csrf_field(); ?>
                                                                <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                                                <input type="hidden" name="engine" value="OTHER">
                                                                <input type="hidden" name="kapal_id" value="<?php echo e($kapal->hash_id); ?>">
                                                                
                                                                <div class="modal-body">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Nama Pemeriksaan</label>
                                                                        <input type="text" class="form-control" value="<?php echo e($notif['item_pemeriksaan']); ?>" readonly>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Kondisi</label>
                                                                        <div class="d-flex gap-2">
                                                                            <input type="hidden" name="kondisi" id="kondisi<?php echo e($notif['id']); ?>" required>
                                                                            <button type="button" class="btn btn-outline-success flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="bagus"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-check-circle me-2"></i>Bagus
                                                                            </button>
                                                                            <button type="button" class="btn btn-outline-danger flex-grow-1 btn-kondisi" 
                                                                                data-target="kondisi<?php echo e($notif['id']); ?>" 
                                                                                data-value="tidak"
                                                                                onclick="setKondisi(this)">
                                                                                <i class="fas fa-times-circle me-2"></i>Tidak Bagus
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Keterangan</label>
                                                                        <textarea class="form-control" name="keterangan" rows="3"></textarea>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <label class="form-label">Tanggal Pemeriksaan</label>
                                                                        <input type="datetime-local" class="form-control" name="tanggal_pemeriksaan" 
                                                                            value="<?php echo e(now()->format('Y-m-d\TH:i')); ?>" required>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    <button type="submit" class="btn btn-primary">Simpan</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Notifikasi Checklist Engine Situasional Khusus (Penggantian Oli Powerpack) -->
        <?php
            $notifSituasionalKhusus = collect($notifications)->filter(function($notif) {
                return $notif['engine'] === 'SITUASIONAL' && $notif['id'] == 174;
            });
        ?>
        <?php if($notifSituasionalKhusus->count() > 0): ?>
        <div class="col-12 mb-4">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-oil-can me-2"></i>
                        Notifikasi Checklist Engine Situasional - Penggantian Oli Powerpack
                        <span class="badge bg-danger ms-2"><?php echo e($notifSituasionalKhusus->count()); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php $__currentLoopData = $notifSituasionalKhusus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="alert alert-info">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-2">
                                    <i class="fas fa-cogs me-2"></i>
                                    <?php echo e($notif['item_pemeriksaan']); ?>

                                </h6>
                                <p class="mb-1">
                                    <strong>Sistem:</strong> <?php echo e($notif['sistem'] ?? '-'); ?>

                                </p>
                                <p class="mb-1">
                                    <strong>Interval:</strong> <?php echo e($notif['interval']); ?> jam
                                </p>
                                <p class="mb-1">
                                    <strong>Running Hours:</strong> <?php echo e(number_format($notif['running_hours'])); ?> jam
                                </p>
                                <p class="mb-1">
                                    <strong>Total Penggantian Oli Mesin:</strong> <?php echo e($notif['oli_mesin_count'] ?? 0); ?>x
                                </p>
                                <p class="mb-1">
                                    <strong>Oli Powerpack Sudah Diganti:</strong> <?php echo e($notif['oli_powerpack_count'] ?? 0); ?>x
                                </p>
                                <p class="mb-0">
                                    <strong>Seharusnya Diganti:</strong> <?php echo e($notif['expected_powerpack_count'] ?? 0); ?>x
                                </p>
                                <small class="text-muted">
                                    <em>Catatan: 2x Oli Ganti Mesin = 1x Ganti Oli Powerpack</em>
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <button type="button" class="btn btn-success btn-lg"
                                    data-bs-toggle="modal"
                                    data-bs-target="#modalPemeriksaan<?php echo e($notif['id']); ?>">
                                    <i class="fas fa-check me-2"></i>Lakukan Penggantian
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Pemeriksaan untuk Oli Powerpack -->
                    <div class="modal fade" id="modalPemeriksaan<?php echo e($notif['id']); ?>" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header bg-success text-white">
                                    <h5 class="modal-title">
                                        <i class="fas fa-oil-can me-2"></i>
                                        Penggantian Oli Powerpack
                                    </h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form id="formPemeriksaan<?php echo e($notif['id']); ?>" action="<?php echo e(route('checklist-engine.store-item')); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="item_id" value="<?php echo e($notif['id']); ?>">
                                    <input type="hidden" name="engine" value="SITUASIONAL">
                                    <input type="hidden" name="kapal_id" value="<?php echo e($kapal->hash_id); ?>">

                                    <div class="modal-body">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle me-2"></i>Informasi Penggantian</h6>
                                            <p class="mb-1">Total Penggantian Oli Mesin: <strong><?php echo e($notif['oli_mesin_count'] ?? 0); ?>x</strong></p>
                                            <p class="mb-1">Oli Powerpack Sudah Diganti: <strong><?php echo e($notif['oli_powerpack_count'] ?? 0); ?>x</strong></p>
                                            <p class="mb-0">Seharusnya Diganti: <strong><?php echo e($notif['expected_powerpack_count'] ?? 0); ?>x</strong></p>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Nama Pemeriksaan</label>
                                            <input type="text" class="form-control" value="<?php echo e($notif['item_pemeriksaan']); ?>" readonly>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Kondisi</label>
                                            <div class="d-flex gap-2">
                                                <input type="hidden" name="kondisi" id="kondisi<?php echo e($notif['id']); ?>" required>
                                                <button type="button" class="btn btn-outline-success flex-grow-1 btn-kondisi"
                                                    data-target="kondisi<?php echo e($notif['id']); ?>"
                                                    data-value="bagus"
                                                    onclick="setKondisi(this)">
                                                    <i class="fas fa-check-circle me-2"></i>Sudah Diganti
                                                </button>
                                                <button type="button" class="btn btn-outline-danger flex-grow-1 btn-kondisi"
                                                    data-target="kondisi<?php echo e($notif['id']); ?>"
                                                    data-value="tidak"
                                                    onclick="setKondisi(this)">
                                                    <i class="fas fa-times-circle me-2"></i>Belum Diganti
                                                </button>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Keterangan</label>
                                            <textarea class="form-control" name="keterangan" rows="3" placeholder="Masukkan keterangan penggantian oli powerpack..."></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Tanggal Pemeriksaan</label>
                                            <input type="datetime-local" class="form-control" name="tanggal_pemeriksaan"
                                                value="<?php echo e(now()->format('Y-m-d\TH:i')); ?>" required>
                                        </div>
                                    </div>

                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>Simpan
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Checklist Modal -->
<div class="modal fade" id="addChecklistModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>Tambah Checklist Engine
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="checklistForm">
                    <div class="draft-status mb-3 text-muted" style="font-size: 0.9em;">
                        <i class="fas fa-save"></i> <span></span>
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <button type="button" class="btn btn-success" id="checkAllGood">
                                        <i class="fas fa-check-double"></i> Centang Semua Ya
                                    </button>
                                </div>
                            </div>
                            <div class="row align-items-center mb-3">
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">Tanggal</label>
                                </div>
                                <div class="col-md-9">
                                    <input type="datetime-local" class="form-control" name="tanggal" required>
                                </div>
                            </div>
                            <div class="row align-items-center mb-3">
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">Call Sign</label>
                                </div>
                                <div class="col-md-9">
                                    <input type="text" class="form-control" value="<?php echo e($kapal->call_sign); ?>" readonly>
                                </div>
                            </div>
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">GT/NT Kapal</label>
                                </div>
                                <div class="col-md-9">
                                    <input type="text" class="form-control" value="<?php echo e($kapal->gt_nt); ?>" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="accordion" id="checklistAccordion">
                        <!-- Main Engine Portside -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#mainEnginePortside">
                                    <i class="fas fa-cogs me-2"></i>Main Engine Portside (MITSUBISHI S6R2-MTK3)
                                </button>
                            </h2>
                            <div id="mainEnginePortside" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['main_engine_portside']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                <?php echo e($item['nama']); ?>

                                                <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                <?php endif; ?>
                                            </label>
                                                    </div>
                                                <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                        <input type="radio" class="btn-check" 
                                                               name="hasil_pemeriksaan[<?php echo e($key); ?>][status]" 
                                                               id="<?php echo e($key); ?>_bagus" 
                                                               value="bagus" required>
                                                        <label class="btn btn-outline-success" for="<?php echo e($key); ?>_bagus">
                                                            <i class="fas fa-check"></i> Ya
                                                        </label>
                                                        
                                                        <input type="radio" class="btn-check" 
                                                               name="hasil_pemeriksaan[<?php echo e($key); ?>][status]" 
                                                               id="<?php echo e($key); ?>_tidak" 
                                                               value="tidak">
                                                        <label class="btn btn-outline-danger" for="<?php echo e($key); ?>_tidak">
                                                            <i class="fas fa-times"></i> Tidak
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                    <input type="text" class="form-control" 
                                                           name="hasil_pemeriksaan[<?php echo e($key); ?>][keterangan]"
                                                           placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Generator Engine Portside -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#generatorEnginePortside">
                                    <i class="fas fa-bolt me-2"></i>Generator Engine Portside (YANMAR 4TNV98-GGE)
                                </button>
                            </h2>
                            <div id="generatorEnginePortside" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['generator_engine_portside']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $systemName => $system): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-4">
                                            <h6 class="mb-3 fw-bold"><?php echo e(ucwords(str_replace('_', ' ', $systemName))); ?></h6>
                                            <?php $__currentLoopData = $system; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="card mb-3">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-12 mb-2">
                                                                <label class="form-label fw-bold">
                                                        <?php echo e($item['nama']); ?>

                                                        <?php if($item['interval']): ?>
                                                                        <span class="badge bg-info ms-2">
                                                                            Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                        </span>
                                                        <?php endif; ?>
                                                    </label>
                                                            </div>
                                                        <div class="col-md-6">
                                                                <div class="btn-group w-100" role="group">
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[generator_<?php echo e($key); ?>][status]" 
                                                                       id="generator_<?php echo e($key); ?>_bagus" 
                                                                       value="bagus" required>
                                                                <label class="btn btn-outline-success" for="generator_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[generator_<?php echo e($key); ?>][status]" 
                                                                       id="generator_<?php echo e($key); ?>_tidak" 
                                                                       value="tidak">
                                                                <label class="btn btn-outline-danger" for="generator_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                                <div class="input-group">
                                                                    <span class="input-group-text bg-light">
                                                                        <i class="fas fa-comment"></i>
                                                                    </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[generator_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Main Engine Starboardside -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#mainEngineStarboardside">
                                    <i class="fas fa-cogs me-2"></i>Main Engine Starboardside (MITSUBISHI S6R2-MTK3L S6R2-MPTK3)
                                </button>
                            </h2>
                            <div id="mainEngineStarboardside" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['main_engine_starboardside']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[starboard_<?php echo e($key); ?>][status]" 
                                                                   id="starboard_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="starboard_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[starboard_<?php echo e($key); ?>][status]" 
                                                                   id="starboard_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="starboard_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[starboard_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                    <!-- Hidden input for running hours -->
                                                    <?php if($item['satuan'] === 'jam'): ?>
                                                        <input type="hidden" 
                                                               name="hasil_pemeriksaan[starboard_<?php echo e($key); ?>][running_hours]"
                                                               value="<?php echo e($item['interval'] ?? ''); ?>">
                                                    <?php elseif(in_array($item['satuan'], ['tahun', 'harian'])): ?>
                                                        <input type="hidden" 
                                                               name="hasil_pemeriksaan[starboard_<?php echo e($key); ?>][running_hours]"
                                                               value="<?php echo e(date('Y-m-d')); ?>">
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Generator Engine Starboardside -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#generatorEngineStarboardside">
                                    <i class="fas fa-bolt me-2"></i>Generator Engine Starboardside (YANMAR 4TNV98-GGE)
                                </button>
                            </h2>
                            <div id="generatorEngineStarboardside" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['generator_engine_starboardside']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $systemName => $system): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-4">
                                            <h6 class="mb-3 fw-bold"><?php echo e(ucwords(str_replace('_', ' ', $systemName))); ?></h6>
                                            <?php $__currentLoopData = $system; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="card mb-3">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-12 mb-2">
                                                                <label class="form-label fw-bold">
                                                                    <?php echo e($item['nama']); ?>

                                                                    <?php if($item['interval']): ?>
                                                                        <span class="badge bg-info ms-2">
                                                                            Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                        </span>
                                                                    <?php endif; ?>
                                                                </label>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="btn-group w-100" role="group">
                                                                    <input type="radio" class="btn-check"
                                                                           name="hasil_pemeriksaan[generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>][status]"
                                                                           id="generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>_bagus"
                                                                           value="bagus" required>
                                                                    <label class="btn btn-outline-success" for="generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>_bagus">
                                                                        <i class="fas fa-check"></i> Ya
                                                                    </label>
                                                                    
                                                                    <input type="radio" class="btn-check"
                                                                           name="hasil_pemeriksaan[generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>][status]"
                                                                           id="generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>_tidak"
                                                                           value="tidak">
                                                                    <label class="btn btn-outline-danger" for="generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>_tidak">
                                                                        <i class="fas fa-times"></i> Tidak
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="input-group">
                                                                    <span class="input-group-text bg-light">
                                                                        <i class="fas fa-comment"></i>
                                                                    </span>
                                                                    <input type="text" class="form-control" 
                                                                           name="hasil_pemeriksaan[generator_starboard_<?php echo e($systemName); ?>_<?php echo e($key); ?>][keterangan]"
                                                                           placeholder="Keterangan">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Generator Engine -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#emergencyGeneratorEngine">
                                    <i class="fas fa-bolt me-2"></i>Emergency Generator Engine (YANMAR 4TNV98-GGE)
                                </button>
                            </h2>
                            <div id="emergencyGeneratorEngine" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['emergency_generator_engine']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $systemName => $system): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-4">
                                            <h6 class="mb-3 fw-bold"><?php echo e(ucwords(str_replace('_', ' ', $systemName))); ?></h6>
                                            <?php $__currentLoopData = $system; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="card mb-3">
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-12 mb-2">
                                                                <label class="form-label fw-bold">
                                                                    <?php echo e($item['nama']); ?>

                                                                    <?php if($item['interval']): ?>
                                                                        <span class="badge bg-info ms-2">
                                                                            Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                        </span>
                                                                    <?php endif; ?>
                                                                </label>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="btn-group w-100" role="group">
                                                                    <?php if($item['nama'] === 'Getaran Tidak Normal'): ?>
                                                                        <input type="radio" class="btn-check" 
                                                                               name="hasil_pemeriksaan[generator_sb_<?php echo e($key); ?>][status]" 
                                                                               id="generator_sb_<?php echo e($key); ?>_bagus" 
                                                                               value="tidak" required>
                                                                        <label class="btn btn-outline-danger" for="generator_sb_<?php echo e($key); ?>_bagus">
                                                                            <i class="fas fa-check"></i> Ya
                                                                        </label>
                                                                        
                                                                        <input type="radio" class="btn-check" 
                                                                               name="hasil_pemeriksaan[generator_sb_<?php echo e($key); ?>][status]" 
                                                                               id="generator_sb_<?php echo e($key); ?>_tidak" 
                                                                               value="bagus">
                                                                        <label class="btn btn-outline-success" for="generator_sb_<?php echo e($key); ?>_tidak">
                                                                            <i class="fas fa-times"></i> Tidak
                                                                        </label>
                                                                    <?php else: ?>
                                                                        <input type="radio" class="btn-check" 
                                                                               name="hasil_pemeriksaan[generator_sb_<?php echo e($key); ?>][status]" 
                                                                               id="generator_sb_<?php echo e($key); ?>_bagus" 
                                                                               value="bagus" required>
                                                                        <label class="btn btn-outline-success" for="generator_sb_<?php echo e($key); ?>_bagus">
                                                                            <i class="fas fa-check"></i> Ya
                                                                        </label>
                                                                        
                                                                        <input type="radio" class="btn-check" 
                                                                               name="hasil_pemeriksaan[generator_sb_<?php echo e($key); ?>][status]" 
                                                                               id="generator_sb_<?php echo e($key); ?>_tidak" 
                                                                               value="tidak">
                                                                        <label class="btn btn-outline-danger" for="generator_sb_<?php echo e($key); ?>_tidak">
                                                                            <i class="fas fa-times"></i> Tidak
                                                                        </label>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="input-group">
                                                                    <span class="input-group-text bg-light">
                                                                        <i class="fas fa-comment"></i>
                                                                    </span>
                                                                    <input type="text" class="form-control" 
                                                                           name="hasil_pemeriksaan[generator_sb_<?php echo e($key); ?>][keterangan]"
                                                                           placeholder="Keterangan">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Gearbox Starboardside -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#gearboxStarboardside">
                                    <i class="fas fa-cog me-2"></i>Gearbox Starboardside
                                </button>
                            </h2>
                            <div id="gearboxStarboardside" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['gearbox_starboardside']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <?php if($item['nama'] === 'Berjalan dengan Tidak Normal' || $item['nama'] === 'Suara Tidak Normal'): ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[gearbox_sb_<?php echo e($key); ?>][status]" 
                                                                       id="gearbox_sb_<?php echo e($key); ?>_bagus" 
                                                                       value="tidak" required>
                                                                <label class="btn btn-outline-danger" for="gearbox_sb_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[gearbox_sb_<?php echo e($key); ?>][status]" 
                                                                       id="gearbox_sb_<?php echo e($key); ?>_tidak" 
                                                                       value="bagus">
                                                                <label class="btn btn-outline-success" for="gearbox_sb_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php else: ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[gearbox_sb_<?php echo e($key); ?>][status]" 
                                                                       id="gearbox_sb_<?php echo e($key); ?>_bagus" 
                                                                       value="bagus" required>
                                                                <label class="btn btn-outline-success" for="gearbox_sb_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[gearbox_sb_<?php echo e($key); ?>][status]" 
                                                                       id="gearbox_sb_<?php echo e($key); ?>_tidak" 
                                                                       value="tidak">
                                                                <label class="btn btn-outline-danger" for="gearbox_sb_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[gearbox_sb_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Fire System -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#fireSystem">
                                    <i class="fas fa-fire me-2"></i>Fire System
                                </button>
                            </h2>
                            <div id="fireSystem" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['fire_system']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <?php if($item['nama'] === 'Suara dan Getaran Tidak Normal'): ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_system_<?php echo e($key); ?>][status]" 
                                                                       id="fire_system_<?php echo e($key); ?>_bagus" 
                                                                       value="tidak" required>
                                                                <label class="btn btn-outline-danger" for="fire_system_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_system_<?php echo e($key); ?>][status]" 
                                                                       id="fire_system_<?php echo e($key); ?>_tidak" 
                                                                       value="bagus">
                                                                <label class="btn btn-outline-success" for="fire_system_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php else: ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_system_<?php echo e($key); ?>][status]" 
                                                                       id="fire_system_<?php echo e($key); ?>_bagus" 
                                                                       value="bagus" required>
                                                                <label class="btn btn-outline-success" for="fire_system_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_system_<?php echo e($key); ?>][status]" 
                                                                       id="fire_system_<?php echo e($key); ?>_tidak" 
                                                                       value="tidak">
                                                                <label class="btn btn-outline-danger" for="fire_system_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[fire_system_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Fire Pump -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#firePump">
                                    <i class="fas fa-fire-extinguisher me-2"></i>Fire Pump
                                </button>
                            </h2>
                            <div id="firePump" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['fire_pump']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <?php if($item['nama'] === 'Suara dan Getaran Tidak Normal' || $item['nama'] === 'Kebocoran dari Blok Pemadam'): ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="fire_pump_<?php echo e($key); ?>_bagus" 
                                                                       value="tidak" required>
                                                                <label class="btn btn-outline-danger" for="fire_pump_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="fire_pump_<?php echo e($key); ?>_tidak" 
                                                                       value="bagus">
                                                                <label class="btn btn-outline-success" for="fire_pump_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php else: ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="fire_pump_<?php echo e($key); ?>_bagus" 
                                                                       value="bagus" required>
                                                                <label class="btn btn-outline-success" for="fire_pump_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="fire_pump_<?php echo e($key); ?>_tidak" 
                                                                       value="tidak">
                                                                <label class="btn btn-outline-danger" for="fire_pump_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[fire_pump_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Fire Pump -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#emergencyFirePump">
                                    <i class="fas fa-fire-extinguisher me-2"></i>Emergency Fire Pump
                                </button>
                            </h2>
                            <div id="emergencyFirePump" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['emergency_fire_pump']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <?php if($item['nama'] === 'Kebocoran dari Blok Pemadam'): ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[emergency_fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="emergency_fire_pump_<?php echo e($key); ?>_bagus" 
                                                                       value="tidak" required>
                                                                <label class="btn btn-outline-danger" for="emergency_fire_pump_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[emergency_fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="emergency_fire_pump_<?php echo e($key); ?>_tidak" 
                                                                       value="bagus">
                                                                <label class="btn btn-outline-success" for="emergency_fire_pump_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php else: ?>
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[emergency_fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="emergency_fire_pump_<?php echo e($key); ?>_bagus" 
                                                                       value="bagus" required>
                                                                <label class="btn btn-outline-success" for="emergency_fire_pump_<?php echo e($key); ?>_bagus">
                                                                    <i class="fas fa-check"></i> Ya
                                                                </label>
                                                                
                                                                <input type="radio" class="btn-check" 
                                                                       name="hasil_pemeriksaan[emergency_fire_pump_<?php echo e($key); ?>][status]" 
                                                                       id="emergency_fire_pump_<?php echo e($key); ?>_tidak" 
                                                                       value="tidak">
                                                                <label class="btn btn-outline-danger" for="emergency_fire_pump_<?php echo e($key); ?>_tidak">
                                                                    <i class="fas fa-times"></i> Tidak
                                                                </label>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[emergency_fire_pump_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Ballast System Port & STBD -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#ballastSystem">
                                    <i class="fas fa-water me-2"></i>Ballast System Port & STBD
                                </button>
                            </h2>
                            <div id="ballastSystem" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['ballast_system']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[ballast_system_<?php echo e($key); ?>][status]" 
                                                                   id="ballast_system_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="ballast_system_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[ballast_system_<?php echo e($key); ?>][status]" 
                                                                   id="ballast_system_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="ballast_system_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[ballast_system_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Bilge and GS System -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#bilgeGsSystem">
                                    <i class="fas fa-ship me-2"></i>Bilge and GS System
                                </button>
                            </h2>
                            <div id="bilgeGsSystem" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['bilge_gs_system']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[bilge_gs_<?php echo e($key); ?>][status]" 
                                                                   id="bilge_gs_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="bilge_gs_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[bilge_gs_<?php echo e($key); ?>][status]" 
                                                                   id="bilge_gs_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="bilge_gs_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[bilge_gs_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Steering -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#emergencySteering">
                                    <i class="fas fa-dharmachakra me-2"></i>Emergency Steering
                                </button>
                            </h2>
                            <div id="emergencySteering" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['emergency_steering']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[emergency_steering_<?php echo e($key); ?>][status]" 
                                                                   id="emergency_steering_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="emergency_steering_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[emergency_steering_<?php echo e($key); ?>][status]" 
                                                                   id="emergency_steering_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="emergency_steering_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[emergency_steering_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Control Consol -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#controlConsol">
                                    <i class="fas fa-gamepad me-2"></i>Control Consol
                                </button>
                            </h2>
                            <div id="controlConsol" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['control_consol']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[control_consol_<?php echo e($key); ?>][status]" 
                                                                   id="control_consol_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="control_consol_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[control_consol_<?php echo e($key); ?>][status]" 
                                                                   id="control_consol_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="control_consol_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[control_consol_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Cleanliness of E/R -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#cleanlinessEr">
                                    <i class="fas fa-broom me-2"></i>Cleanliness of E/R
                                </button>
                            </h2>
                            <div id="cleanlinessEr" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['cleanliness_er']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[cleanliness_er_<?php echo e($key); ?>][status]" 
                                                                   id="cleanliness_er_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="cleanliness_er_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[cleanliness_er_<?php echo e($key); ?>][status]" 
                                                                   id="cleanliness_er_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="cleanliness_er_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[cleanliness_er_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Closing Valves -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#quickClosingValves">
                                    <i class="fas fa-tachometer-alt me-2"></i>Quick Closing Valves
                                </button>
                            </h2>
                            <div id="quickClosingValves" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <?php $__currentLoopData = $engineItems['quick_closing_valves']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="card mb-3">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-2">
                                                        <label class="form-label fw-bold">
                                                            <?php echo e($item['nama']); ?>

                                                            <?php if($item['interval']): ?>
                                                                <span class="badge bg-info ms-2">
                                                                    Interval: <?php echo e($item['interval']); ?> <?php echo e($item['satuan']); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="btn-group w-100" role="group">
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[quick_closing_valves_<?php echo e($key); ?>][status]" 
                                                                   id="quick_closing_valves_<?php echo e($key); ?>_bagus" 
                                                                   value="bagus" required>
                                                            <label class="btn btn-outline-success" for="quick_closing_valves_<?php echo e($key); ?>_bagus">
                                                                <i class="fas fa-check"></i> Ya
                                                            </label>
                                                            
                                                            <input type="radio" class="btn-check" 
                                                                   name="hasil_pemeriksaan[quick_closing_valves_<?php echo e($key); ?>][status]" 
                                                                   id="quick_closing_valves_<?php echo e($key); ?>_tidak" 
                                                                   value="tidak">
                                                            <label class="btn btn-outline-danger" for="quick_closing_valves_<?php echo e($key); ?>_tidak">
                                                                <i class="fas fa-times"></i> Tidak
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="input-group">
                                                            <span class="input-group-text bg-light">
                                                                <i class="fas fa-comment"></i>
                                                            </span>
                                                            <input type="text" class="form-control" 
                                                                   name="hasil_pemeriksaan[quick_closing_valves_<?php echo e($key); ?>][keterangan]"
                                                                   placeholder="Keterangan">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-body">
                            <label class="form-label fw-bold">Keterangan Umum</label>
                            <textarea class="form-control" name="keterangan" rows="3" 
                                    placeholder="Masukkan keterangan umum jika ada..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-warning" id="clearDraft">
                    <i class="fas fa-trash"></i> Hapus Draft
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="submitChecklist">
                    <i class="fas fa-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Checklist Modal -->
<div class="modal fade" id="viewChecklistModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>Detail Checklist Engine
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Info Kapal -->
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-alt text-info fs-4 me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Tanggal Pemeriksaan</small>
                                        <strong id="view_tanggal"></strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-ship text-info fs-4 me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Nama Kapal</small>
                                        <strong id="view_nama_kapal"></strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-broadcast-tower text-info fs-4 me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">Call Sign</small>
                                        <strong id="view_call_sign"></strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-weight text-info fs-4 me-2"></i>
                                    <div>
                                        <small class="text-muted d-block">GT/NT Kapal</small>
                                        <strong id="view_gt_kapal"></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hasil Pemeriksaan -->
                <div class="accordion" id="viewChecklistAccordion">
                    <!-- Hasil akan di-render disini -->
                </div>

                <!-- Keterangan -->
                <div class="card mt-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>Keterangan Umum
                        </h6>
                    </div>
                    <div class="card-body">
                        <p id="view_keterangan" class="mb-0"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="showDownloadModal()" id="downloadBtn">
                    <i class="fas fa-download me-2"></i>
                    Download PDF
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Download Modal -->
<div class="modal fade" id="downloadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-download me-2"></i>
                    Download Checklist
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="downloadForm">
                    <div class="row g-3 mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" name="running_hour_me_ps" required>
                                <label>Running Hour ME PS</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" name="running_hour_me_sb" required>
                                <label>Running Hour ME SB</label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 mb-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" name="tempat_pemeriksaan" required>
                                <label>Tempat Pemeriksaan</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="date" class="form-control" name="tanggal_pemeriksaan" required>
                                <label>Tanggal Pemeriksaan</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-floating">
                            <input type="text" class="form-control" name="yang_memeriksa" required>
                            <label>Yang Memeriksa</label>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" name="kkm" required>
                                <label>Nama KKM</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" name="masinis_2" required>
                                <label>Nama Masinis II</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="text" class="form-control" name="masinis_3" required>
                                <label>Nama Masinis III</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="confirmDownload">
                    <i class="fas fa-download me-2"></i>
                    Download PDF
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tabel Hasil Pemeriksaan -->
<div class="col-12">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-clipboard-check me-2"></i>
                Hasil Checklist Engine
            </h5>
        </div>
        <div class="card-body">
            <!-- Running Hours Info -->
            <div class="alert alert-info mb-3">
                <div class="d-flex align-items-center">
                    <i class="fas fa-clock fa-2x me-3"></i>
                    <div>
                        <h6 class="mb-1">Running Hours Saat Ini:</h6>
                        <div class="d-flex gap-4">
                            <span><strong>ME PS:</strong> <?php echo e(number_format($kapal->rh_me_ps)); ?> jam</span>
                            <span><strong>ME SB:</strong> <?php echo e(number_format($kapal->rh_me_sb)); ?> jam</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search dan Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <form action="" method="GET" class="d-flex">
                        <input type="text" name="search" class="form-control me-2" placeholder="Cari..." value="<?php echo e(request()->search); ?>">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <form action="<?php echo e(route('checklist-engine.export', HashIdHelper::encode($kapal->id))); ?>" method="GET" class="d-flex gap-2">
                        <select name="tujuan" class="form-select" required>
                            <option value="">Pilih Tujuan</option>
                            <?php $__currentLoopData = $tujuanList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tujuan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($tujuan); ?>"><?php echo e($tujuan); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-file-excel me-1"></i>
                            Export Excel
                        </button>
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>
                                <a href="<?php echo e(request()->fullUrlWithQuery([
                                    'sort_by' => 'tanggal',
                                    'sort_order' => request()->sort_by == 'tanggal' && request()->sort_order == 'asc' ? 'desc' : 'asc'
                                ])); ?>" class="text-decoration-none text-dark">
                                    Tanggal
                                    <?php if(request()->sort_by == 'tanggal'): ?>
                                        <i class="fas fa-sort-<?php echo e(request()->sort_order == 'asc' ? 'up' : 'down'); ?>"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo e(request()->fullUrlWithQuery([
                                    'sort_by' => 'item',
                                    'sort_order' => request()->sort_by == 'item' && request()->sort_order == 'asc' ? 'desc' : 'asc'
                                ])); ?>" class="text-decoration-none text-dark">
                                    Item Pemeriksaan
                                    <?php if(request()->sort_by == 'item'): ?>
                                        <i class="fas fa-sort-<?php echo e(request()->sort_order == 'asc' ? 'up' : 'down'); ?>"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo e(request()->fullUrlWithQuery([
                                    'sort_by' => 'kondisi',
                                    'sort_order' => request()->sort_by == 'kondisi' && request()->sort_order == 'asc' ? 'desc' : 'asc'
                                ])); ?>" class="text-decoration-none text-dark">
                                    Kondisi
                                    <?php if(request()->sort_by == 'kondisi'): ?>
                                        <i class="fas fa-sort-<?php echo e(request()->sort_order == 'asc' ? 'up' : 'down'); ?>"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo e(request()->fullUrlWithQuery([
                                    'sort_by' => 'keterangan',
                                    'sort_order' => request()->sort_by == 'keterangan' && request()->sort_order == 'asc' ? 'desc' : 'asc'
                                ])); ?>" class="text-decoration-none text-dark">
                                    Keterangan
                                    <?php if(request()->sort_by == 'keterangan'): ?>
                                        <i class="fas fa-sort-<?php echo e(request()->sort_order == 'asc' ? 'up' : 'down'); ?>"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="<?php echo e(request()->fullUrlWithQuery([
                                    'sort_by' => 'tujuan',
                                    'sort_order' => request()->sort_by == 'tujuan' && request()->sort_order == 'asc' ? 'desc' : 'asc'
                                ])); ?>" class="text-decoration-none text-dark">
                                    Tujuan
                                    <?php if(request()->sort_by == 'tujuan'): ?>
                                        <i class="fas fa-sort-<?php echo e(request()->sort_order == 'asc' ? 'up' : 'down'); ?>"></i>
                                    <?php else: ?>
                                        <i class="fas fa-sort"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $hasilPemeriksaan; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $hasil): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e(($hasilPemeriksaan->currentPage() - 1) * $hasilPemeriksaan->perPage() + $loop->iteration); ?></td>
                            <td><?php echo e(\Carbon\Carbon::parse($hasil->tanggal_pemeriksaan)->format('d/m/Y H:i')); ?></td>
                            <td><?php echo e($hasil->item_pemeriksaan); ?></td>
                            <td>
                                <?php if($hasil->kondisi == 'bagus'): ?>
                                    <span class="badge bg-success">Bagus</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Tidak Bagus</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($hasil->keterangan); ?></td>
                            <td>
                                <?php echo e($hasil->tujuan ?? '-'); ?><br>
                                <small class="text-muted">
                                    RH ME PS: <?php echo e(number_format($hasil->rh_me_ps ?? 0)); ?> jam<br>
                                    RH ME SB: <?php echo e(number_format($hasil->rh_me_sb ?? 0)); ?> jam
                                </small>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center">Tidak ada data</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Menampilkan <?php echo e($hasilPemeriksaan->firstItem() ?? 0); ?> - <?php echo e($hasilPemeriksaan->lastItem() ?? 0); ?> dari <?php echo e($hasilPemeriksaan->total()); ?> data
                </div>
                <div>
                    <?php echo e($hasilPemeriksaan->links()); ?>

                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    // URGENT FIX: Aggressive backdrop removal and modal positioning
    (function() {
        // Remove ALL backdrops immediately and continuously
        function removeAllBackdrops() {
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }

        // Remove backdrops immediately
        removeAllBackdrops();

        // Keep removing backdrops every 100ms
        setInterval(removeAllBackdrops, 100);

        // Also remove on any DOM changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.classList && node.classList.contains('modal-backdrop')) {
                            node.remove();
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('URGENT: Aggressive backdrop removal system activated');
    })();

    // URGENT: Override Bootstrap Modal to prevent backdrop creation
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const originalShow = bootstrap.Modal.prototype.show;
        bootstrap.Modal.prototype.show = function() {
            // Check if this is a pemeriksaan modal
            if (this._element && this._element.id.includes('modalPemeriksaan')) {
                // Don't use Bootstrap for pemeriksaan modals
                return;
            }
            // Use original show for other modals
            return originalShow.apply(this, arguments);
        };
    }

    // Global function to manually clean up modal issues (can be called from console)
    window.cleanupModalBackdrop = function() {
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        console.log('Manual modal cleanup completed');
    };

    // Function to bring modal to front with extreme z-index values
    function bringModalToFront(modal) {
        // Set extremely high z-index values
        modal.style.zIndex = '1000002';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.display = 'block';

        const modalDialog = modal.querySelector('.modal-dialog');
        const modalContent = modal.querySelector('.modal-content');

        if (modalDialog) {
            modalDialog.style.zIndex = '1000003';
            modalDialog.style.position = 'relative';
            modalDialog.style.pointerEvents = 'auto';
        }

        if (modalContent) {
            modalContent.style.zIndex = '1000004';
            modalContent.style.position = 'relative';
            modalContent.style.pointerEvents = 'auto';
        }

        // Ensure backdrop is behind modal
        setTimeout(() => {
            const backdrop = document.querySelector('.modal-backdrop.show');
            if (backdrop) {
                backdrop.style.zIndex = '999998';
                backdrop.style.position = 'fixed';
            }
        }, 10);

        // Force modal to be visible and on top
        setTimeout(() => {
            modal.style.display = 'block';
            modal.classList.add('show');
        }, 20);
    }

    // Function to aggressively force modal to top layer
    function forceModalToTop(modal) {
        // Remove any existing positioning
        modal.style.cssText = '';

        // Apply extreme positioning
        modal.style.zIndex = '1000002';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100vw';
        modal.style.height = '100vh';
        modal.style.display = 'block';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';

        // Force all child elements to inherit high z-index
        const allChildren = modal.querySelectorAll('*');
        allChildren.forEach(child => {
            child.style.position = 'relative';
            child.style.zIndex = 'inherit';
        });

        // Specifically target modal components
        const modalDialog = modal.querySelector('.modal-dialog');
        const modalContent = modal.querySelector('.modal-content');

        if (modalDialog) {
            modalDialog.style.zIndex = '1000003';
            modalDialog.style.position = 'relative';
            modalDialog.style.pointerEvents = 'auto';
            modalDialog.style.margin = '1.75rem auto';
        }

        if (modalContent) {
            modalContent.style.zIndex = '1000004';
            modalContent.style.position = 'relative';
            modalContent.style.pointerEvents = 'auto';
        }

        // Ensure backdrop is properly positioned
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.style.zIndex = '999998';
                backdrop.style.position = 'fixed';
            });
        }, 10);
    }

    // Ultimate fallback: Use maximum z-index value
    function useMaximumZIndex(modal) {
        const maxZIndex = '2147483647'; // Maximum possible z-index value

        modal.style.zIndex = maxZIndex;
        modal.style.position = 'fixed';
        modal.style.display = 'block';

        const modalDialog = modal.querySelector('.modal-dialog');
        const modalContent = modal.querySelector('.modal-content');

        if (modalDialog) {
            modalDialog.style.zIndex = maxZIndex;
            modalDialog.style.position = 'relative';
        }

        if (modalContent) {
            modalContent.style.zIndex = maxZIndex;
            modalContent.style.position = 'relative';
        }

        // Set backdrop to lower z-index
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.style.zIndex = '2147483646'; // One less than maximum
                backdrop.style.position = 'fixed';
            });
        }, 10);
    }
    // Fungsi untuk menampilkan form tambah
    function showFormTambah() {
        document.getElementById('formTambahPerjalanan').style.display = 'block';
    }

    // Fungsi untuk menyembunyikan form tambah
    function hideFormTambah() {
        document.getElementById('formTambahPerjalanan').style.display = 'none';
    }

    // Fungsi untuk sorting
    document.addEventListener('DOMContentLoaded', function() {
        const table = document.getElementById('historiPerjalananTable');
        const headers = table.querySelectorAll('th.sortable');
        let currentSort = { column: 'tanggal', direction: 'asc' };

        headers.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.sort;
                
                // Reset semua header
                headers.forEach(h => {
                    h.classList.remove('asc', 'desc');
                });

                // Toggle sort direction
                if (currentSort.column === column) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort = { column, direction: 'asc' };
                }

                // Add sort class
                header.classList.add(currentSort.direction);

                // Sort data
                const rows = Array.from(table.querySelectorAll('tbody tr'));
                const sortedRows = rows.sort((a, b) => {
                    let aVal = a.children[getColumnIndex(column)].textContent;
                    let bVal = b.children[getColumnIndex(column)].textContent;

                    // Convert to numbers if sorting RH columns
                    if (column === 'rh_me_ps' || column === 'rh_me_sb') {
                        aVal = parseInt(aVal.replace(/,/g, ''));
                        bVal = parseInt(bVal.replace(/,/g, ''));
                    }

                    if (currentSort.direction === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });

                // Update table
                const tbody = table.querySelector('tbody');
                tbody.innerHTML = '';
                sortedRows.forEach((row, index) => {
                    row.querySelector('td').textContent = index + 1;
                    tbody.appendChild(row);
                });
            });
        });

        function getColumnIndex(column) {
            switch(column) {
                case 'tanggal': return 1;
                case 'rh_me_ps': return 2;
                case 'rh_me_sb': return 3;
                case 'tujuan': return 4;
                default: return 0;
            }
        }
    });

    // Event handler untuk form tambah perjalanan
    document.getElementById('formUpdatePerjalanan')?.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Tampilkan loading
        Swal.fire({
            title: 'Memproses...',
            html: 'Mohon tunggu sebentar',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Kirim form dengan fetch
        fetch(this.action, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json'
            },
            body: new FormData(this)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                }).then(() => {
                    window.location.reload();
                });
            } else {
                Swal.fire({
                    title: 'Gagal!',
                    text: data.message || 'Gagal menambah perjalanan',
                    icon: 'error'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan saat menambah perjalanan',
                icon: 'error'
            });
        });
    });

    // Fungsi untuk menangani delete histori
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.btn-delete-histori').forEach(button => {
            button.addEventListener('click', function() {
                const historiId = this.getAttribute('data-id');
                const deleteButton = this;

                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Apakah Anda yakin ingin menghapus histori perjalanan ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Tampilkan loading
                        Swal.fire({
                            title: 'Memproses...',
                            html: 'Mohon tunggu sebentar',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        fetch(`<?php echo e(url('checklist-engine/delete-histori/' . $kapal->hash_id)); ?>/${historiId}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: 'Histori perjalanan berhasil dihapus',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                }).then(() => {
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Gagal!',
                                    text: data.message || 'Gagal menghapus histori perjalanan',
                                    icon: 'error'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'Error!',
                                text: 'Terjadi kesalahan saat menghapus histori perjalanan',
                                icon: 'error'
                            });
                        });
                    }
                });
            });
        });

        // SweetAlert untuk tombol delete checklist
        document.querySelectorAll('.delete-checklist').forEach(button => {
            button.addEventListener('click', function() {
                const checklistId = this.getAttribute('data-id');
                
                Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Apakah Anda yakin ingin menghapus checklist ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Tampilkan loading
                        Swal.fire({
                            title: 'Memproses...',
                            html: 'Mohon tunggu sebentar',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        fetch(`<?php echo e(url('checklist-engine')); ?>/${checklistId}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: 'Checklist berhasil dihapus',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                }).then(() => {
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Gagal!',
                                    text: data.message || 'Gagal menghapus checklist',
                                    icon: 'error'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'Error!',
                                text: 'Terjadi kesalahan saat menghapus checklist',
                                icon: 'error'
                            });
                        });
                    }
                });
            });
        });
    });

    // Fungsi untuk set kondisi pemeriksaan
    function setKondisi(button) {
        const targetId = button.getAttribute('data-target');
        const value = button.getAttribute('data-value');
        document.getElementById(targetId).value = value;
        
        // Reset semua button di group yang sama
        button.closest('.d-flex').querySelectorAll('.btn-kondisi').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-value') === 'bagus') {
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-success');
            } else {
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-outline-danger');
            }
        });
        
        // Set active state
        button.classList.add('active');
        if (value === 'bagus') {
            button.classList.remove('btn-outline-success');
            button.classList.add('btn-success');
        } else {
            button.classList.remove('btn-outline-danger');
            button.classList.add('btn-danger');
        }
    }

    // Submit checklist dengan SweetAlert
    document.getElementById('submitChecklist')?.addEventListener('click', function() {
        Swal.fire({
            title: 'Konfirmasi Simpan',
            text: 'Apakah Anda yakin ingin menyimpan checklist ini?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, Simpan!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Tampilkan loading
                Swal.fire({
                    title: 'Memproses...',
                    html: 'Mohon tunggu sebentar',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Kirim form dengan fetch
                const form = document.getElementById('checklistForm');
                fetch(form.action, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json'
                    },
                    body: new FormData(form)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: data.message,
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        }).then(() => {
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Gagal!',
                            text: data.message,
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menyimpan data',
                        icon: 'error'
                    });
                });
            }
        });
    });

    // Clear draft dengan SweetAlert
    document.getElementById('clearDraft')?.addEventListener('click', function() {
        Swal.fire({
            title: 'Konfirmasi Hapus Draft',
            text: 'Apakah Anda yakin ingin menghapus draft checklist ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                localStorage.removeItem('checklistDraft');
                Swal.fire({
                    title: 'Berhasil!',
                    text: 'Draft checklist berhasil dihapus',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                }).then(() => {
                    window.location.reload();
                });
            }
        });
    });

    // Fungsi untuk menangani submit form pemeriksaan
    function handleFormPemeriksaan(formId) {
        const form = document.getElementById(formId);
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Tampilkan loading
            Swal.fire({
                title: 'Memproses...',
                html: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Kirim form dengan fetch
            fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: new FormData(form)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    // Tutup modal terlebih dahulu
                    const modal = form.closest('.modal');
                    if (modal) {
                        const modalInstance = bootstrap.Modal.getInstance(modal);
                        if (modalInstance) {
                            modalInstance.hide();
                        }
                    }

                    // Tampilkan pesan sukses
                    Swal.fire({
                        title: 'Berhasil!',
                        text: data.message,
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    }).then(() => {
                        // Pastikan backdrop dibersihkan sebelum reload
                        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                            backdrop.remove();
                        });
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';

                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: data.message,
                        icon: 'error'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data',
                    icon: 'error'
                });
            });
        });
    }

    // Inisialisasi handler untuk setiap form pemeriksaan
    document.addEventListener('DOMContentLoaded', function() {
        // Cari semua form yang ID-nya dimulai dengan 'formPemeriksaan'
        document.querySelectorAll('form[id^="formPemeriksaan"]').forEach(form => {
            handleFormPemeriksaan(form.id);
        });

        // Fix modal backdrop issues
        initializeModalBackdropFix();
    });

    // Fungsi untuk mengatasi masalah modal backdrop
    function initializeModalBackdropFix() {
        // Hapus semua backdrop yang mungkin tertinggal saat halaman dimuat
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.remove();
        });

        // Pastikan body tidak memiliki class modal-open yang tertinggal
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Event handler untuk semua modal pemeriksaan
        document.querySelectorAll('[id^="modalPemeriksaan"]').forEach(modal => {
            // Set extremely high z-index directly on modal element
            modal.style.zIndex = '1000002';
            modal.style.position = 'fixed';

            const modalInstance = new bootstrap.Modal(modal, {
                backdrop: true,
                keyboard: true,
                focus: true
            });

            // Event saat modal akan ditampilkan
            modal.addEventListener('show.bs.modal', function(e) {
                // Pastikan tidak ada backdrop lain yang tertinggal
                document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                    if (backdrop !== e.target.querySelector('.modal-backdrop')) {
                        backdrop.remove();
                    }
                });

                // Pastikan modal berada di layer paling atas dengan z-index ekstrem
                modal.style.zIndex = '1000002';
                modal.style.position = 'fixed';
                modal.style.display = 'block';
                const modalDialog = modal.querySelector('.modal-dialog');
                const modalContent = modal.querySelector('.modal-content');
                if (modalDialog) {
                    modalDialog.style.zIndex = '1000003';
                    modalDialog.style.position = 'relative';
                }
                if (modalContent) {
                    modalContent.style.zIndex = '1000004';
                    modalContent.style.position = 'relative';
                }
            });

            // Event saat modal ditampilkan
            modal.addEventListener('shown.bs.modal', function(e) {
                // Pastikan modal dapat di-scroll dan di-interact
                const modalDialog = modal.querySelector('.modal-dialog');
                const modalContent = modal.querySelector('.modal-content');

                if (modalDialog) {
                    modalDialog.style.pointerEvents = 'auto';
                }

                if (modalContent) {
                    modalContent.style.pointerEvents = 'auto';
                }

                // Pastikan backdrop memiliki z-index yang tepat (di bawah modal)
                const backdrop = document.querySelector('.modal-backdrop.show');
                if (backdrop) {
                    backdrop.style.zIndex = '999998';
                    backdrop.style.position = 'fixed';
                }

                // Focus pada modal untuk memastikan interaksi
                modal.focus();
            });

            // Event saat modal akan disembunyikan
            modal.addEventListener('hide.bs.modal', function(e) {
                // Reset form jika ada
                const form = modal.querySelector('form');
                if (form) {
                    // Reset kondisi buttons
                    form.querySelectorAll('.btn-kondisi').forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.getAttribute('data-value') === 'bagus') {
                            btn.classList.remove('btn-success');
                            btn.classList.add('btn-outline-success');
                        } else {
                            btn.classList.remove('btn-danger');
                            btn.classList.add('btn-outline-danger');
                        }
                    });
                }
            });

            // Event saat modal sudah disembunyikan
            modal.addEventListener('hidden.bs.modal', function(e) {
                // Pastikan backdrop dihapus
                setTimeout(() => {
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                        backdrop.remove();
                    });

                    // Pastikan body class dan style direset jika tidak ada modal lain yang terbuka
                    if (!document.querySelector('.modal.show')) {
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    }
                }, 100);
            });
        });

        // URGENT FIX: Custom modal system that bypasses Bootstrap backdrop
        document.querySelectorAll('[data-bs-toggle="modal"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const targetModal = document.querySelector(this.getAttribute('data-bs-target'));
                if (targetModal && targetModal.id.includes('modalPemeriksaan')) {
                    // CUSTOM MODAL SYSTEM - NO BOOTSTRAP
                    showCustomModal(targetModal);
                } else if (targetModal) {
                    // Regular modal handling for non-pemeriksaan modals
                    const modalInstance = bootstrap.Modal.getOrCreateInstance(targetModal);
                    modalInstance.show();
                }
            });
        });

        // Custom modal system for pemeriksaan modals
        function showCustomModal(modal) {
            console.log('Opening custom modal:', modal.id);

            // Remove any existing custom modals
            document.querySelectorAll('.custom-modal-system').forEach(el => el.remove());

            // Remove ALL backdrops
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());

            // Hide original modal
            modal.style.display = 'none';

            // Create custom modal overlay
            const overlay = document.createElement('div');
            overlay.className = 'custom-modal-system';
            overlay.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, 0.5) !important;
                z-index: 2147483647 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                pointer-events: auto !important;
                padding: 20px !important;
                box-sizing: border-box !important;
                overflow-y: auto !important;
            `;

            // Clone modal content
            const modalDialog = modal.querySelector('.modal-dialog');
            if (!modalDialog) {
                console.error('Modal dialog not found');
                return;
            }

            const clonedDialog = modalDialog.cloneNode(true);

            // Create a wrapper div for better control
            const modalWrapper = document.createElement('div');
            modalWrapper.style.cssText = `
                max-width: 500px !important;
                width: 90% !important;
                max-height: 90vh !important;
                margin: 0 auto !important;
                position: relative !important;
                z-index: 2147483647 !important;
            `;

            // Style the cloned dialog
            clonedDialog.style.cssText = `
                margin: 0 !important;
                width: 100% !important;
                position: relative !important;
                z-index: 2147483647 !important;
            `;

            // Ensure modal content has proper styling
            const modalContent = clonedDialog.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.cssText = `
                    background: white !important;
                    border: none !important;
                    border-radius: 15px !important;
                    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
                    width: 100% !important;
                    position: relative !important;
                    z-index: 2147483647 !important;
                    display: block !important;
                `;
            }

            modalWrapper.appendChild(clonedDialog);

            overlay.appendChild(modalWrapper);

            // Handle close buttons
            modalWrapper.querySelectorAll('[data-bs-dismiss="modal"], .btn-close').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    overlay.remove();
                });
            });

            // Handle all possible submit buttons
            modalWrapper.querySelectorAll('button').forEach(btn => {
                const btnText = btn.textContent.trim().toLowerCase();
                if (btnText === 'simpan' || btn.type === 'submit' || btn.classList.contains('btn-primary')) {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Submit button clicked:', btnText);

                        const form = modalWrapper.querySelector('form');
                        if (form) {
                            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                            form.dispatchEvent(submitEvent);
                        }
                    });
                }
            });

            // Handle backdrop click
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    overlay.remove();
                }
            });

            // Handle form submission
            const form = modalWrapper.querySelector('form');
            if (form) {
                // Debug: Log all inputs in the form
                console.log('Form found:', form.id);
                console.log('Form action:', form.action);
                const formInputs = form.querySelectorAll('input, textarea, select');
                console.log('Form inputs count:', formInputs.length);
                formInputs.forEach((input, index) => {
                    console.log(`Input ${index}:`, {
                        name: input.name,
                        id: input.id,
                        type: input.type,
                        value: input.value
                    });
                });

                // Ensure all required hidden inputs exist in form
                const requiredHiddenInputs = ['kondisi', 'item_id', 'kapal_id'];

                requiredHiddenInputs.forEach(inputName => {
                    let input = form.querySelector(`input[name="${inputName}"]`);
                    if (!input) {
                        // Look for it in modalWrapper
                        input = modalWrapper.querySelector(`input[name="${inputName}"]`);
                        if (input) {
                            // Move to form
                            form.appendChild(input);
                            console.log(`Moved existing ${inputName} input to form`);
                        } else if (inputName === 'kondisi') {
                            // Create kondisi input if it doesn't exist
                            input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = 'kondisi';
                            input.required = true;
                            form.appendChild(input);
                            console.log('Created new kondisi input in form');
                        }
                    }
                });
                // Copy kondisi button handlers
                modalWrapper.querySelectorAll('.btn-kondisi').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('Kondisi button clicked:', this.getAttribute('data-value'));

                        const targetId = this.getAttribute('data-target');
                        const value = this.getAttribute('data-value');

                        // Find the hidden input - try multiple selectors
                        let hiddenInput = null;

                        // Method 1: By ID
                        if (targetId) {
                            hiddenInput = modalWrapper.querySelector(`#${targetId}`);
                            console.log('Method 1 (by ID):', targetId, hiddenInput);
                        }

                        // Method 2: By name attribute
                        if (!hiddenInput) {
                            hiddenInput = modalWrapper.querySelector(`input[name="kondisi"]`);
                            console.log('Method 2 (by name):', hiddenInput);
                        }

                        // Method 3: In form scope
                        if (!hiddenInput) {
                            hiddenInput = form.querySelector(`input[name="kondisi"]`);
                            console.log('Method 3 (in form):', hiddenInput);
                        }

                        // Method 4: Create if not exists
                        if (!hiddenInput) {
                            hiddenInput = document.createElement('input');
                            hiddenInput.type = 'hidden';
                            hiddenInput.name = 'kondisi';
                            form.appendChild(hiddenInput);
                            console.log('Method 4 (created):', hiddenInput);
                        }

                        if (hiddenInput) {
                            hiddenInput.value = value;
                            console.log('Hidden input updated:', hiddenInput.name || hiddenInput.id, '=', value);

                            // Trigger change event
                            hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
                        } else {
                            console.error('All methods failed to find/create hidden input');
                        }

                        // Update button states in the same group
                        const buttonGroup = this.closest('.d-flex');
                        buttonGroup.querySelectorAll('.btn-kondisi').forEach(b => {
                            b.classList.remove('active', 'btn-success', 'btn-danger');
                            if (b.getAttribute('data-value') === 'bagus') {
                                b.classList.remove('btn-success');
                                b.classList.add('btn-outline-success');
                            } else {
                                b.classList.remove('btn-danger');
                                b.classList.add('btn-outline-danger');
                            }
                        });

                        // Set active state for clicked button
                        this.classList.add('active');
                        if (value === 'bagus') {
                            this.classList.remove('btn-outline-success');
                            this.classList.add('btn-success');
                        } else {
                            this.classList.remove('btn-outline-danger');
                            this.classList.add('btn-danger');
                        }
                    });
                });

                // Handle submit button click
                const submitBtn = modalWrapper.querySelector('button[type="submit"], .btn-primary');
                if (submitBtn) {
                    submitBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        submitForm();
                    });
                }

                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitForm();
                });

                function submitForm() {
                    console.log('Submitting form...');

                    // Debug: Check all form inputs
                    const allInputs = form.querySelectorAll('input, textarea, select');
                    console.log('All form inputs:');
                    allInputs.forEach(input => {
                        console.log(`${input.name}: ${input.value}`);
                    });

                    // Validate all required fields
                    const requiredFields = [
                        { name: 'item_id', label: 'Item ID' },
                        { name: 'kapal_id', label: 'Kapal ID' },
                        { name: 'tanggal_pemeriksaan', label: 'Tanggal Pemeriksaan' },
                        { name: 'kondisi', label: 'Kondisi' }
                    ];

                    let missingFields = [];

                    requiredFields.forEach(field => {
                        let input = form.querySelector(`input[name="${field.name}"]`);

                        // If not found in form, try in modalWrapper
                        if (!input) {
                            input = modalWrapper.querySelector(`input[name="${field.name}"]`);
                            if (input) {
                                form.appendChild(input);
                                console.log(`Moved ${field.name} input to form`);
                            }
                        }

                        console.log(`${field.name} input found:`, input);
                        console.log(`${field.name} input value:`, input ? input.value : 'not found');

                        if (!input || !input.value) {
                            if (field.name === 'kondisi') {
                                // Try to find active kondisi button and set value
                                const activeKondisiBtn = modalWrapper.querySelector('.btn-kondisi.active');
                                if (activeKondisiBtn) {
                                    const value = activeKondisiBtn.getAttribute('data-value');

                                    // Create kondisi input if it doesn't exist
                                    if (!input) {
                                        input = document.createElement('input');
                                        input.type = 'hidden';
                                        input.name = 'kondisi';
                                        input.required = true;
                                        form.appendChild(input);
                                        console.log('Created kondisi input in form');
                                    }

                                    input.value = value;
                                    console.log('Fixed kondisi value from active button:', value);
                                } else {
                                    missingFields.push('Kondisi (silakan pilih kondisi)');
                                }
                            } else {
                                missingFields.push(field.label);
                            }
                        }
                    });

                    if (missingFields.length > 0) {
                        Swal.fire({
                            title: 'Peringatan!',
                            text: `Field berikut harus diisi: ${missingFields.join(', ')}`,
                            icon: 'warning',
                            customClass: {
                                container: 'swal-high-z-index'
                            }
                        });
                        return;
                    }

                    // Show loading
                    Swal.fire({
                        title: 'Memproses...',
                        html: 'Mohon tunggu sebentar',
                        allowOutsideClick: false,
                        customClass: {
                            container: 'swal-high-z-index'
                        },
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Get form data
                    const formData = new FormData(form);

                    // Debug form data
                    console.log('Form action:', form.action);
                    console.log('Form method:', form.method);
                    for (let [key, value] of formData.entries()) {
                        console.log('Form data:', key, '=', value);
                    }

                    // Submit form
                    fetch(form.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json'
                        },
                        body: formData
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Response data:', data);
                        overlay.remove();

                        if (data.status) {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false,
                                customClass: {
                                    container: 'swal-high-z-index'
                                }
                            }).then(() => {
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: data.message,
                                icon: 'error',
                                customClass: {
                                    container: 'swal-high-z-index'
                                }
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);

                        // Fallback: try traditional form submission
                        console.log('Trying traditional form submission...');

                        // Create a temporary form in the original modal
                        const originalModal = document.querySelector(`#${modal.id}`);
                        const originalForm = originalModal.querySelector('form');

                        if (originalForm) {
                            // Copy form data to original form
                            const formData = new FormData(form);
                            for (let [key, value] of formData.entries()) {
                                const originalInput = originalForm.querySelector(`[name="${key}"]`);
                                if (originalInput) {
                                    originalInput.value = value;
                                }
                            }

                            // Submit original form
                            originalForm.submit();
                            overlay.remove();
                        } else {
                            overlay.remove();
                            Swal.fire({
                                title: 'Error!',
                                text: 'Terjadi kesalahan saat menyimpan data',
                                icon: 'error',
                                customClass: {
                                    container: 'swal-high-z-index'
                                }
                            });
                        }
                    });
                }
            }

            document.body.appendChild(overlay);

            // Focus on first input
            setTimeout(() => {
                const firstInput = modalWrapper.querySelector('input, select, textarea');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);

            console.log('Custom modal created and added to DOM');
        }

        // Event handler untuk tombol close modal
        document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const modal = this.closest('.modal');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }
            });
        });

        // Event handler untuk klik pada backdrop
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                const modalInstance = bootstrap.Modal.getInstance(e.target);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\sikapal\resources\views/checklist-engine/index.blade.php ENDPATH**/ ?>