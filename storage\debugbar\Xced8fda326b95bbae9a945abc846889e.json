{"__meta": {"id": "Xced8fda326b95bbae9a945abc846889e", "datetime": "2025-07-20 07:17:05", "utime": **********.44099, "method": "GET", "uri": "/pembelian-kantor?page=1&search=PRESSURE+TRANSMITER%09", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.13591, "end": **********.441012, "duration": 0.3051018714904785, "duration_str": "305ms", "measures": [{"label": "Booting", "start": **********.13591, "relative_start": 0, "end": **********.33251, "relative_end": **********.33251, "duration": 0.19659996032714844, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.332525, "relative_start": 0.19661498069763184, "end": **********.441015, "relative_end": 3.0994415283203125e-06, "duration": 0.108489990234375, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26459424, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "admin_kantor.pembelian.index", "param_count": null, "params": [], "start": **********.424101, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/pembelian/index.blade.phpadmin_kantor.pembelian.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fpembelian%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": **********.428401, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.434679, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.435579, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.436706, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET pembelian-kantor", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\PembelianKantorController@index", "namespace": null, "prefix": "", "where": [], "as": "pembelian-kantor.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=15\" onclick=\"\">app/Http/Controllers/PembelianKantorController.php:15-44</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.022410000000000003, "accumulated_duration_str": "22.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.376194, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.380452, "duration": 0.019850000000000003, "duration_str": "19.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 88.577}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where exists (select * from `data_sparepart` where `pembelian_kantor`.`id_barang` = `data_sparepart`.`id` and (`nama_barang` like '%PRESSURE TRANSMITER%' or `nomor_seri` like '%PRESSURE TRANSMITER%')) or `keterangan` like '%PRESSURE TRANSMITER%'", "type": "query", "params": [], "bindings": ["%PRESSURE TRANSMITER%", "%PRESSURE TRANSMITER%", "%PRESSURE TRANSMITER%"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.406492, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:38", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=38", "ajax": false, "filename": "PembelianKantorController.php", "line": "38"}, "connection": "gema_kapal", "explain": null, "start_percent": 88.577, "width_percent": 4.507}, {"sql": "select * from `pembelian_kantor` where exists (select * from `data_sparepart` where `pembelian_kantor`.`id_barang` = `data_sparepart`.`id` and (`nama_barang` like '%PRESSURE TRANSMITER%' or `nomor_seri` like '%PRESSURE TRANSMITER%')) or `keterangan` like '%PRESSURE TRANSMITER%' order by `tanggal` desc, `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["%PRESSURE TRANSMITER%", "%PRESSURE TRANSMITER%", "%PRESSURE TRANSMITER%"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.408608, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:38", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=38", "ajax": false, "filename": "PembelianKantorController.php", "line": "38"}, "connection": "gema_kapal", "explain": null, "start_percent": 93.083, "width_percent": 2.9}, {"sql": "select * from `data_sparepart` where `data_sparepart`.`id` in (226)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 38}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4103382, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:38", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=38", "ajax": false, "filename": "PembelianKantorController.php", "line": "38"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.984, "width_percent": 1.205}, {"sql": "select `id`, `nomor_seri`, `nama_barang`, `satuan`, `jenis` from `data_sparepart`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 41}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.412017, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:41", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=41", "ajax": false, "filename": "PembelianKantorController.php", "line": "41"}, "connection": "gema_kapal", "explain": null, "start_percent": 97.189, "width_percent": 2.811}]}, "models": {"data": {"App\\Models\\DataSparepart": {"value": 130, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDataSparepart.php&line=1", "ajax": false, "filename": "DataSparepart.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PembelianKantor": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FPembelianKantor.php&line=1", "ajax": false, "filename": "PembelianKantor.php", "line": "?"}}}, "count": 132, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/pembelian-kantor?page=1&search=PRESSURE%20TRANSMITER%09\"\n]"}, "request": {"path_info": "/pembelian-kantor", "status_code": "<pre class=sf-dump id=sf-dump-1640449718 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1640449718\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-655949491 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"19 characters\">PRESSURE TRANSMITER</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655949491\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1921815783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1921815783\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2014468088 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://sikapal.test/pembelian-kantor?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im5wT1JTcmZIZTNXdkhPVVc2bTBvWnc9PSIsInZhbHVlIjoiVFluVzFmY1RabG1LWmVkZHFuTW13bmh0Z0hDSWpwcTZndEdiZlJqRzVuc2xSTGlsQzdtYnBIcU91d0tJNWdXV0hIc0lOMnVxYXdScXlOMEx5NVJ4L3RJUlZrMk15TXlHWG9WMGZNRGg1RU1udEtrcFRYdWV0NjhYR3hsbWxSSlgiLCJtYWMiOiJmNzkzMjQyM2RhMTRlOGFmZWUxYWRkMWFjNzJhZTVkODA5MWI1NWVhYTNlYzM2NjgxN2RkMzRmOTIzZTk0OTI3IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IlV6bjZOUmFHS2diVi9LZmxpM3dSS2c9PSIsInZhbHVlIjoidDVJcFdPY2phMzFEK29BNmh1bTAxdW9kbzJadjgyT1Y3Yms1QTY5MTEvYktaNFdvVmNGY3pKZXErUHdKbUI2UW10eWhDcmJpbXZWWXE2Wk5GT1BIc2lVaUxySmc2eExzZllwdmNPbzNpSnRuSUY5eVo2aFUyaUlvR2V3a3VHSjkiLCJtYWMiOiIxM2E0YjViZmVjNDk2MWU0YWM0NmMyY2UxYTFjMWZiNzA4ODIxN2Q2M2QzYzQzMzMwMDA0NmUwZDlmZjc0YmJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014468088\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1509345646 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509345646\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1913696038 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:17:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJEa2x4eXlYOFJqem9FTG5JTlVieVE9PSIsInZhbHVlIjoiYTVsUGF2L3JpRVBJbWlrZzBkUXJZODhnVkhKNTFsOUUvempWbmxLdHBwaGhQSU5VVnlSRi9iN0dpWitJNlJwT1gvdmdyanV6VUhRS0VnaDVjTE1ydWNWUmtzNlhIbVFKWUNnMmRRcncxSlI5VFovUFMyR1BPdnBPekdXNlFLY2QiLCJtYWMiOiI1YjhiYmUyNDBlZWJkNjg5YzYxNGY5ODE5YjZjYzczYjFmYjgwMWVlM2NhNTFjNWNkMDk1N2E2MGQ4ZDUzMTRjIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:17:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImtzNlJSY1ZIeEpaMzkraFhreWhYSmc9PSIsInZhbHVlIjoiMTcwcXhVOVl6bGl5Y2hXNHUwUFJCY2VrajN5eXNaOVdDSmUreS9nYjhWTFlVRjZHUnZwcnQwYk1IK2kzT3pjYVZ3NVl5b3hyaWk2M1pBQlB1NjNmMVp1WjNMSlduOENNR2hGMERYV016S1ZTRjkyVjIwZ3dVaDlvU3FtNE9CWEgiLCJtYWMiOiJlYTBlOGNlZDljNzVhZDc4MzdiNmYwMjE4MjU1NGE1ZTgzOWRjNmUzMzAwYTc4MTgyNzNlMmZjNmU0MTM3YjNhIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:17:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJEa2x4eXlYOFJqem9FTG5JTlVieVE9PSIsInZhbHVlIjoiYTVsUGF2L3JpRVBJbWlrZzBkUXJZODhnVkhKNTFsOUUvempWbmxLdHBwaGhQSU5VVnlSRi9iN0dpWitJNlJwT1gvdmdyanV6VUhRS0VnaDVjTE1ydWNWUmtzNlhIbVFKWUNnMmRRcncxSlI5VFovUFMyR1BPdnBPekdXNlFLY2QiLCJtYWMiOiI1YjhiYmUyNDBlZWJkNjg5YzYxNGY5ODE5YjZjYzczYjFmYjgwMWVlM2NhNTFjNWNkMDk1N2E2MGQ4ZDUzMTRjIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:17:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImtzNlJSY1ZIeEpaMzkraFhreWhYSmc9PSIsInZhbHVlIjoiMTcwcXhVOVl6bGl5Y2hXNHUwUFJCY2VrajN5eXNaOVdDSmUreS9nYjhWTFlVRjZHUnZwcnQwYk1IK2kzT3pjYVZ3NVl5b3hyaWk2M1pBQlB1NjNmMVp1WjNMSlduOENNR2hGMERYV016S1ZTRjkyVjIwZ3dVaDlvU3FtNE9CWEgiLCJtYWMiOiJlYTBlOGNlZDljNzVhZDc4MzdiNmYwMjE4MjU1NGE1ZTgzOWRjNmUzMzAwYTc4MTgyNzNlMmZjNmU0MTM3YjNhIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:17:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913696038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1347398387 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"75 characters\">http://sikapal.test/pembelian-kantor?page=1&amp;search=PRESSURE%20TRANSMITER%09</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347398387\", {\"maxDepth\":0})</script>\n"}}