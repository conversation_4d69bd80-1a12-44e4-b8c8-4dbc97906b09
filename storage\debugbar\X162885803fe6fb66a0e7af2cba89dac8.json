{"__meta": {"id": "X162885803fe6fb66a0e7af2cba89dac8", "datetime": "2025-07-20 07:16:44", "utime": **********.61135, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.344416, "end": **********.61138, "duration": 0.2669641971588135, "duration_str": "267ms", "measures": [{"label": "Booting", "start": **********.344416, "relative_start": 0, "end": **********.526647, "relative_end": **********.526647, "duration": 0.18223118782043457, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.526661, "relative_start": 0.18224501609802246, "end": **********.611384, "relative_end": 3.814697265625e-06, "duration": 0.08472299575805664, "duration_str": "84.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24760080, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET dashboard", "middleware": "web, auth, admin.kapal", "controller": "App\\Http\\Controllers\\DashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=15\" onclick=\"\">app/Http/Controllers/DashboardController.php:15-180</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01559, "accumulated_duration_str": "15.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.568575, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.572741, "duration": 0.01559, "duration_str": "15.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-387351551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-387351551\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1782456899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1782456899\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2036934102 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://sikapal.test/riwayat-pembelian?page=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkZpMitaN1JMV3Z5QURDb20xdUZvVWc9PSIsInZhbHVlIjoicCtjNFdIRVp5WVVRQmlNd3ZveC9ZNzFDWm5kRjYyUnF6QkNra1FzeExXc3ROQXU5SUZjMkJOOU9BRlRUMk90K1NCUXRldnJ6QUM0SGVXN2JZdFJXaUZ1K2lVM2xkUEFZYkEreHkzZXR6cWRBSHRWRFVxckxmWXlRMUxGeFVIVXgiLCJtYWMiOiI1Zjc4ODRjODJkMTc2NWMyMDA0OGE3MGE3ZTM0NWEzODVhYzQ3Y2M3MmVlMWRiNzFmNzlhMDZhMzNmMTYxZmYxIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6InpqRnBuVjRXT1RDOUM1eGd4T1d6b1E9PSIsInZhbHVlIjoibHNmSWh2TGtqdkNRZngwd0dESXpTdUZtZCt4ZkJiLzBCbmEwZVZvQ3YwTmgzckNZV2xJU20vZzF2OWZZMDRWTC9DUTQyTTlpaGNaTUlJYll4dWFmLzN0S1FZK0V2SzBTSktpZnpDa0NtWUVzMWxkRXdKVTJGa2pxRXYzWHJZN1giLCJtYWMiOiJmMjQ0MmE3MzkwODAzNDIwMDY5NDBkMDQ2MDliMDNjYzA0YWE4YzE5Mjk3NjI4MmM1ZTJjMmU5OWQ0YTU1MGY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036934102\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1975115478 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975115478\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-504193627 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:16:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpKSzVwNm5TbUFsS0V0QkkraVBzNXc9PSIsInZhbHVlIjoiTGc0MmgzV1lyZ1VrcmdTZktqTDJuREd1MkQ1TUFrRHF3d2tGU2lXTGNnWHErbm5pR1JsZWgzU0JjSWtoMUU2aGo5TjBUZGw2dmYvWVhwaE9GTHpXTjcyWVFuWkpsS2xmOHlFNjV4bCtRUEswMndiWTMxS3JxN0g2UWozOU1Ya1EiLCJtYWMiOiI5MGYyMzFhODBlNzJkMGVmOTg2MjYzMzZmNTU2Njg2MDYyMGVmYzRjYmZlYWY3N2ZhZDVhYmY5ZGMxYTI0YTc4IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:16:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IjR4T1NEZU5yTHNBTTlNYzZhNld4cVE9PSIsInZhbHVlIjoiQVE0RkdGN2RRaVFGMVl0TEVxN1ZlR0hwNnB2OGtpNWRDQXQwbnJRcy9sZUpraVJiOU9SanFkTkhKcTM0NmNkMTV6eis5NTF0UnJOTTdXOFBwa2t2cE9hcWlQQW9zVnBEeGJZQXVJQ1N6dnMyUTZ6UU1ZMnc4b2R2T0hOTzVUdGsiLCJtYWMiOiJjZjM2MTVmMTc4ODYzZmYzMzk1ZTJhOTgzNTI0ZTY4ZmE2MDA0NThjN2U3ZmZhZjU5NDYyZWRiYTJhZTBiNzZkIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:16:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpKSzVwNm5TbUFsS0V0QkkraVBzNXc9PSIsInZhbHVlIjoiTGc0MmgzV1lyZ1VrcmdTZktqTDJuREd1MkQ1TUFrRHF3d2tGU2lXTGNnWHErbm5pR1JsZWgzU0JjSWtoMUU2aGo5TjBUZGw2dmYvWVhwaE9GTHpXTjcyWVFuWkpsS2xmOHlFNjV4bCtRUEswMndiWTMxS3JxN0g2UWozOU1Ya1EiLCJtYWMiOiI5MGYyMzFhODBlNzJkMGVmOTg2MjYzMzZmNTU2Njg2MDYyMGVmYzRjYmZlYWY3N2ZhZDVhYmY5ZGMxYTI0YTc4IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:16:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IjR4T1NEZU5yTHNBTTlNYzZhNld4cVE9PSIsInZhbHVlIjoiQVE0RkdGN2RRaVFGMVl0TEVxN1ZlR0hwNnB2OGtpNWRDQXQwbnJRcy9sZUpraVJiOU9SanFkTkhKcTM0NmNkMTV6eis5NTF0UnJOTTdXOFBwa2t2cE9hcWlQQW9zVnBEeGJZQXVJQ1N6dnMyUTZ6UU1ZMnc4b2R2T0hOTzVUdGsiLCJtYWMiOiJjZjM2MTVmMTc4ODYzZmYzMzk1ZTJhOTgzNTI0ZTY4ZmE2MDA0NThjN2U3ZmZhZjU5NDYyZWRiYTJhZTBiNzZkIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:16:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504193627\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784570727 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://sikapal.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784570727\", {\"maxDepth\":0})</script>\n"}}