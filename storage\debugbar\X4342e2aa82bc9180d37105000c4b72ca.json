{"__meta": {"id": "X4342e2aa82bc9180d37105000c4b72ca", "datetime": "2025-07-20 07:56:56", "utime": 1752973016.032597, "method": "GET", "uri": "/checklist-engine/2NaDK50q4p", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[07:56:54] LOG.info: Kapal: {\n    \"id\": 4,\n    \"kapal\": {\n        \"id\": 4,\n        \"nama\": \"TB. GEMA 201\",\n        \"rh_me_ps\": \"12623\",\n        \"rh_me_sb\": \"12618\",\n        \"jenis_kapal_id\": 1,\n        \"created_at\": \"2024-12-16T09:41:48.000000Z\",\n        \"updated_at\": \"2025-07-14T12:43:33.000000Z\",\n        \"gt_nt\": \"204\\/62\",\n        \"port_of_registry\": \"SAMARINDA\",\n        \"tanda_pendaftaran\": \"2022 IIk No. 9810\\/L\",\n        \"call_sign\": \"YDC 6210\",\n        \"imo\": \"9982706\",\n        \"mesin\": \"MITSUBISHI 2X 759 KW\",\n        \"tanda_selar\": \"GT. 204 No. 7085\\/IIK\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.572351, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.106748, "end": 1752973016.032622, "duration": 1.9258739948272705, "duration_str": "1.93s", "measures": [{"label": "Booting", "start": **********.106748, "relative_start": 0, "end": **********.390603, "relative_end": **********.390603, "duration": 0.28385496139526367, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.390619, "relative_start": 0.2838709354400635, "end": 1752973016.032626, "relative_end": 3.814697265625e-06, "duration": 1.6420068740844727, "duration_str": "1.64s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 33310792, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "checklist-engine.index", "param_count": null, "params": [], "start": **********.998812, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/checklist-engine/index.blade.phpchecklist-engine.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fchecklist-engine%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": 1752973015.993731, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": 1752973016.009979, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": 1752973016.01159, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": 1752973016.01276, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": 1752973016.025128, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET checklist-engine/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ChecklistEngineController@index", "namespace": null, "prefix": "", "where": [], "as": "checklist-engine.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=19\" onclick=\"\">app/Http/Controllers/ChecklistEngineController.php:19-133</a>"}, "queries": {"nb_statements": 341, "nb_visible_statements": 342, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13497000000000023, "accumulated_duration_str": "135ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 241 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.529843, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `kapal` where `kapal`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.533928, "duration": 0.01306, "duration_str": "13.06ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 9.676}, {"sql": "select * from `jenis_kapal` where `jenis_kapal`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.692068, "duration": 0.01207, "duration_str": "12.07ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:34", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=34", "ajax": false, "filename": "ChecklistEngineController.php", "line": "34"}, "connection": "gema_kapal", "explain": null, "start_percent": 9.676, "width_percent": 8.943}, {"sql": "select * from `histori_per<PERSON>lanan_kapal` where `kapal_id` = 4 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7056668, "duration": 0.00657, "duration_str": "6.57ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:47", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=47", "ajax": false, "filename": "ChecklistEngineController.php", "line": "47"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.619, "width_percent": 4.868}, {"sql": "select `hpe`.*, `cde`.`item_pemeriksaan` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`history_id` = 24", "type": "query", "params": [], "bindings": [4, 24], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.713756, "duration": 0.0075, "duration_str": "7.5ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:55", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=55", "ajax": false, "filename": "ChecklistEngineController.php", "line": "55"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.487, "width_percent": 5.557}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.725377, "duration": 0.006849999999999999, "duration_str": "6.85ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.043, "width_percent": 5.075}, {"sql": "select `hpe`.*, `cde`.`item_pemeri<PERSON><PERSON>`, `hpk`.`tujuan`, `hpk`.`rh_me_ps`, `hpk`.`rh_me_sb` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON>n_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 order by `hpe`.`tanggal_pemeriksaan` desc, `hpe`.`created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.733397, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.119, "width_percent": 0.704}, {"sql": "select count(*) as aggregate from `checklist_engines` where `kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.758363, "duration": 0.01005, "duration_str": "10.05ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:99", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=99", "ajax": false, "filename": "ChecklistEngineController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.823, "width_percent": 7.446}, {"sql": "select * from `checklist_data_engine` order by `waktu` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.7914698, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:24", "source": {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=24", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.269, "width_percent": 0.822}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.796534, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.091, "width_percent": 0.496}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 42 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 42, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7982712, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.587, "width_percent": 0.852}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.800683, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.44, "width_percent": 0.363}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.80253, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.803, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8040211, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.084, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 44 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 44, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.805327, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.299, "width_percent": 0.363}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.806938, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.662, "width_percent": 0.43}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.80866, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.092, "width_percent": 0.296}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.810121, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.388, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 54 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 54, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.811387, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.603, "width_percent": 0.348}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8129032, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.951, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.814256, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.166, "width_percent": 0.356}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 58 limit 1", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8159368, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.522, "width_percent": 0.178}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 58 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 58, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8171341, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.699, "width_percent": 0.37}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8186681, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.07, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.820006, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.285, "width_percent": 0.296}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8215148, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.581, "width_percent": 0.296}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 65 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 65, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.822977, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.878, "width_percent": 0.378}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8245332, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.255, "width_percent": 0.2}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.82586, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.455, "width_percent": 0.296}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.827327, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.752, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 72 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 72, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.828629, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.967, "width_percent": 0.333}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.830262, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.3, "width_percent": 0.296}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.831753, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.596, "width_percent": 0.326}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 87 limit 1", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.833265, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.922, "width_percent": 0.222}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 87 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 87, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8345869, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.145, "width_percent": 0.57}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.836486, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.715, "width_percent": 0.207}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.837883, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.923, "width_percent": 0.259}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 88 limit 1", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.839357, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.182, "width_percent": 0.185}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 88 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 88, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.84063, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.367, "width_percent": 0.341}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.842179, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.708, "width_percent": 0.2}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8435981, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.908, "width_percent": 0.333}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.845138, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 53.241, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 94 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 94, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.846424, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 53.471, "width_percent": 0.378}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.84798, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 53.849, "width_percent": 0.23}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.849398, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 54.079, "width_percent": 0.304}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8508742, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 54.382, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 96 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 96, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8521268, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 54.597, "width_percent": 0.296}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.853599, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 54.894, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8549259, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 55.109, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 99 limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.856421, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 55.39, "width_percent": 0.207}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 99 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 99, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.85768, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 55.598, "width_percent": 0.341}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8591762, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 55.938, "width_percent": 0.222}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.860594, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 56.161, "width_percent": 0.356}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 106 limit 1", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.86225, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 56.516, "width_percent": 0.259}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 106 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 106, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.863604, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 56.776, "width_percent": 0.356}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8651369, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 57.131, "width_percent": 0.163}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8663929, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 57.294, "width_percent": 0.289}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 109 limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.867846, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 57.583, "width_percent": 0.222}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 109 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 109, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.869123, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 57.805, "width_percent": 0.304}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.870721, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 58.109, "width_percent": 0.267}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8721402, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 58.376, "width_percent": 0.333}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.873667, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 58.709, "width_percent": 0.207}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 112 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 112, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8749118, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 58.917, "width_percent": 0.319}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 118 limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8764122, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.235, "width_percent": 0.207}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 118 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 118, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.87779, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.443, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 120 limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.879206, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.724, "width_percent": 0.126}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 120 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 120, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.88034, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 59.85, "width_percent": 0.267}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 123 limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8817341, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.117, "width_percent": 0.104}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 123 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 123, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.882781, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.221, "width_percent": 0.267}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.884197, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.488, "width_percent": 0.081}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 131 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 131, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.885283, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.569, "width_percent": 0.333}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 134 limit 1", "type": "query", "params": [], "bindings": [134], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8867671, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.902, "width_percent": 0.111}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 134 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 134, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.88786, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.014, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 138 limit 1", "type": "query", "params": [], "bindings": [138], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8891761, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.243, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 138 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 138, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8902292, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.34, "width_percent": 0.2}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 139 limit 1", "type": "query", "params": [], "bindings": [139], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.891554, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.54, "width_percent": 0.119}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 139 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 139, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8926501, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.658, "width_percent": 0.326}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 140 limit 1", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.894271, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.984, "width_percent": 0.148}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 140 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 140, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.895423, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 62.132, "width_percent": 0.289}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 141 limit 1", "type": "query", "params": [], "bindings": [141], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.896892, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 62.421, "width_percent": 0.089}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 141 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 141, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.897977, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 62.51, "width_percent": 0.207}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 143 limit 1", "type": "query", "params": [], "bindings": [143], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.899299, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 62.718, "width_percent": 0.178}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 143 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 143, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.900568, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 62.895, "width_percent": 0.311}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9019802, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 63.207, "width_percent": 0.104}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9031382, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 63.31, "width_percent": 0.237}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9045131, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 63.547, "width_percent": 0.237}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 18 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 18, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.905887, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 63.785, "width_percent": 0.319}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.907344, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 64.103, "width_percent": 0.126}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 22 limit 1", "type": "query", "params": [], "bindings": [4, 22], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.908523, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 64.229, "width_percent": 0.407}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 50 limit 1", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.910265, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 64.637, "width_percent": 0.141}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 50 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 50, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.911402, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 64.777, "width_percent": 0.296}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.912823, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 65.074, "width_percent": 0.119}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.913995, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 65.192, "width_percent": 0.244}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.915393, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 65.437, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 52 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 52, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9164438, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 65.533, "width_percent": 0.304}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9178941, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 65.837, "width_percent": 0.111}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.919068, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 65.948, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9204102, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:99", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=99", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 66.178, "width_percent": 0.081}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 93 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 93, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.921437, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:112", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 112}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=112", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "112"}, "connection": "gema_kapal", "explain": null, "start_percent": 66.259, "width_percent": 0.274}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.922788, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:122", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 122}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=122", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "122"}, "connection": "gema_kapal", "explain": null, "start_percent": 66.533, "width_percent": 0.119}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 48}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.923934, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:138", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=138", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "138"}, "connection": "gema_kapal", "explain": null, "start_percent": 66.652, "width_percent": 0.237}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925302, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.889, "width_percent": 0.237}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925788, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.126, "width_percent": 0.17}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.926115, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.296, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9263391, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.371, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9267402, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.6, "width_percent": 0.081}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.926962, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.682, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927197, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.822, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927359, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.904, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927562, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927749, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.067, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927974, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.2, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928131, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.282, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9283228, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.371, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9284961, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.43, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9287121, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.556, "width_percent": 0.074}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9288552, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.63, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929044, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.719, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929221, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.778, "width_percent": 0.119}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929456, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.897, "width_percent": 0.148}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929798, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.045, "width_percent": 0.133}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930055, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.178, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93024, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.238, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9304662, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.371, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93065, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.467, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9308572, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.564, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931064, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.638, "width_percent": 0.126}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931303, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.764, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93152, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.838, "width_percent": 0.126}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931766, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.964, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931968, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.03, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932199, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.149, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9323971, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.208, "width_percent": 0.385}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932991, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.593, "width_percent": 0.207}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.933339, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.801, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.933753, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.031, "width_percent": 0.178}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934112, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.208, "width_percent": 0.148}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934381, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.357, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934585, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.431, "width_percent": 0.126}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9348292, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.557, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935023, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.623, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935236, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.749, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935404, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.831, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935647, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.927, "width_percent": 0.178}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936148, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.105, "width_percent": 0.289}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936604, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.394, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936796, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.49, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9370232, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.594, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937217, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.661, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9374561, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.801, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937621, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.883, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937823, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.979, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938029, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.046, "width_percent": 0.333}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938539, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.379, "width_percent": 0.111}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938766, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.49, "width_percent": 0.252}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.939212, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.742, "width_percent": 0.148}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9395618, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.89, "width_percent": 0.282}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940041, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.172, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940276, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.268, "width_percent": 0.148}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940547, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.417, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940754, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.491, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9409811, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.624, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.941139, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.705, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9413512, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.809, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.941546, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.876, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9417691, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.009, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.941941, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.091, "width_percent": 0.207}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942322, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.298, "width_percent": 0.081}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942551, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.38, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9428022, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.528, "width_percent": 0.089}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9429731, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.617, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943192, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.721, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943384, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.787, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943618, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.928, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943782, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.009, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9439712, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.098, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.944164, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.165, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.94438, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.291, "width_percent": 0.074}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9445322, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.365, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.944726, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.454, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.944905, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.513, "width_percent": 0.274}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945318, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.787, "width_percent": 0.074}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945471, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.862, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9456592, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.95, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945853, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.017, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946069, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.143, "width_percent": 0.074}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946239, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.217, "width_percent": 0.222}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.94664, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.439, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946915, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.536, "width_percent": 0.267}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947366, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.802, "width_percent": 0.207}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947764, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.01, "width_percent": 0.156}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9480472, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.166, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.948252, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.24, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.948496, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.358, "width_percent": 0.207}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.948911, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.566, "width_percent": 0.163}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949186, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.729, "width_percent": 0.089}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949367, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.818, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9495912, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.921, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949794, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.988, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950016, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.121, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950181, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.203, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950371, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.292, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950565, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.358, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9507792, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.484, "width_percent": 0.074}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950931, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.558, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9511268, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.647, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951308, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.707, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9515188, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.833, "width_percent": 0.193}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951824, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.025, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9520211, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.122, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952206, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.188, "width_percent": 0.126}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952452, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.314, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9526381, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.373, "width_percent": 0.111}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9528568, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.485, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.95304, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.544, "width_percent": 0.319}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953566, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.862, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953809, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.959, "width_percent": 0.148}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9540882, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.107, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954286, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.174, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954507, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.292, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954704, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.359, "width_percent": 0.341}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955244, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.7, "width_percent": 0.244}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.95565, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.944, "width_percent": 0.141}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9559312, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.085, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9561439, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.159, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9563942, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.307, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956557, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.389, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9567642, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.485, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956957, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.552, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957178, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.685, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957344, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.767, "width_percent": 0.089}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9575312, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.855, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957722, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.922, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957936, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.048, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958097, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.13, "width_percent": 0.207}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958457, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.337, "width_percent": 0.178}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958832, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.515, "width_percent": 0.2}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9591582, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.715, "width_percent": 0.089}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959335, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.804, "width_percent": 0.207}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959702, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.011, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959958, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.108, "width_percent": 0.2}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960305, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.308, "width_percent": 0.126}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960553, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.434, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960802, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.552, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961016, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.626, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9612691, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.774, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9614692, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.856, "width_percent": 0.215}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9619489, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.071, "width_percent": 0.17}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.962281, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.241, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9625082, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.382, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.962688, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.463, "width_percent": 0.17}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963035, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.634, "width_percent": 0.222}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963448, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.856, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9636931, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.004, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963854, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.086, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9640632, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.182, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.964268, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.249, "width_percent": 0.244}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.964678, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.493, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965059, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.708, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965485, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.938, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965929, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.168, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966181, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.316, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966347, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.397, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966559, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.494, "width_percent": 0.059}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966743, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.553, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966975, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.679, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967343, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.894, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967608, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.012, "width_percent": 0.163}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967972, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.175, "width_percent": 0.237}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9683561, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.412, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.968714, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.627, "width_percent": 0.111}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9689512, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.738, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969154, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.805, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969388, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.946, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969554, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.027, "width_percent": 0.096}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9697652, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.124, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.96996, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.19, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970203, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.324, "width_percent": 0.163}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970497, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.487, "width_percent": 0.148}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970812, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.635, "width_percent": 0.267}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.971318, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.901, "width_percent": 0.215}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.971668, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.116, "width_percent": 0.215}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.972023, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.331, "width_percent": 0.126}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9722989, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.457, "width_percent": 0.185}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.972717, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.642, "width_percent": 0.23}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973102, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.872, "width_percent": 0.341}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973645, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.213, "width_percent": 0.474}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.974413, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.687, "width_percent": 0.156}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9747891, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.843, "width_percent": 0.356}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.975546, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.198, "width_percent": 0.185}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9759488, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.383, "width_percent": 0.17}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976298, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.554, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976519, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.628, "width_percent": 0.126}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976772, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.754, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976971, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.821, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9772131, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.939, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977406, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.006, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977638, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.147, "width_percent": 0.089}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977814, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.236, "width_percent": 0.111}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978039, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.347, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978234, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.413, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978465, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.532, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.97871, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.628, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9790351, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.791, "width_percent": 0.119}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.97942, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.91, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979741, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.073, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979958, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.147, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9801931, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.265, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980382, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.332, "width_percent": 0.126}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980595, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.458, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980762, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.54, "width_percent": 0.111}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.651, "width_percent": 0.17}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981364, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.821, "width_percent": 0.259}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981764, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.08, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981951, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.177, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982171, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.28, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982373, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.347, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9826, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.48, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9827611, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.562, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982979, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.666, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.983178, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.732, "width_percent": 0.141}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.983412, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.873, "width_percent": 0.089}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98358, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.962, "width_percent": 0.104}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.983791, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.066, "width_percent": 0.067}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98398, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.132, "width_percent": 0.133}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9842012, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.266, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9843822, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.347, "width_percent": 0.215}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.984797, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.562, "width_percent": 0.156}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9852529, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.718, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985495, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.866, "width_percent": 0.104}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985687, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.97, "width_percent": 0.119}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985922, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.088, "width_percent": 0.074}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986124, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.162, "width_percent": 0.148}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9863741, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.311, "width_percent": 0.081}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986534, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.392, "width_percent": 0.104}, {"sql": "select distinct `tujuan` from `histori_perjalanan_kapal` where `kapal_id` = ? and `tujuan` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986844, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.496, "width_percent": 0.133}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973015.972656, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.629, "width_percent": 0.763}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973015.973799, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.392, "width_percent": 0.445}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973015.974838, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.837, "width_percent": 0.267}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973015.975327, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.104, "width_percent": 0.222}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973015.975712, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.326, "width_percent": 0.237}, {"sql": "select * from `users` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752973016.022558, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.563, "width_percent": 0.437}]}, "models": {"data": {"App\\Models\\ChecklistDataEngine": {"value": 272, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=1", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "?"}}, "App\\Models\\HistoriPerjalananKapal": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FHistoriPerjalananKapal.php&line=1", "ajax": false, "filename": "HistoriPerjalananKapal.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\JenisKapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FJenisKapal.php&line=1", "ajax": false, "filename": "JenisKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 293, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/checklist-engine/2NaDK50q4p\"\n]"}, "request": {"path_info": "/checklist-engine/2NaDK50q4p", "status_code": "<pre class=sf-dump id=sf-dump-1856623451 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1856623451\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1521947666 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1521947666\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2060095073 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2060095073\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1776508924 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikc2ZFFHRkcvdUpKREExdUFESHBaTFE9PSIsInZhbHVlIjoidDJGUjVENVJiTS9tNUd6Rzl1c09OSUxsSVAwTTFiN2Y1bS9YU2Z5b1djYnd2ck5ZNFVqY2FrbjlSaklyK09nVzgxK05FQW9QSVNEZ0FpRVE3SGtBQzVHdkNtNEoyMDR3cHZMaVgwZEdua0hqd0E4ODZjTFBDbW9EY3VUcVhOM3UiLCJtYWMiOiJkNWY5ODk3MTBiZDQ5OTk1NjJhY2VmZDMwN2ZkYjc5ZDdmNmM3NjdjZWQ1MWRkZTgzNmM0MzJiMDExMjgzY2UxIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IkNrYjAyQ3psemFoTHM0c1dwWmhMWHc9PSIsInZhbHVlIjoiVUkxam1sZSsxOUVrSER5S1I0VmNuMXZOOVVSWC9rV0tXR1BhRU4xS0xtTU96TlZQaDN1QTk4RExmNXdZZGlnNURCa3QvQ2NFcm9mMGdpelk0QVhHb3RCR1RBSHFpOVpQYWVSTTN0ajdCc1ZWMFpxWlFsa3NWbWpuU1FoZjQrdWgiLCJtYWMiOiJjODMyNzUzY2QzMTEzNmU1MTQxOTI5MGU2MjQ1OTc3YzY2YTZkMDJkODQ2N2U1OGI0M2EyZTc4YjM1ODM1Zjg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776508924\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1577839187 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577839187\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:56:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InUrZHE5djlyVE5XbDd5cHVBdGJnYmc9PSIsInZhbHVlIjoiUSt2bkExdEw0OGxQMnFpbGw1K0hCU1VuR2RueUlEamRWdWI3dW56amV6MHEya09zQ3drL1dsVWcyTVYvbWJmVWc4MWRUTUJwamg3YlRvZnpydTZ6N0E1bWtueDNDd296OG42WWtBNmlsR0c3QUhaT2lBTHg4SWgxNCtGWHlaSzgiLCJtYWMiOiI1MDI5YTU4YzZmZWVjMzJhZjc0ZDU4YWMyMDY5MTA4ZmNmNDI4ZmNmNWI0NGZlYmY2MThmMjBjNmFlNmRhNTI1IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:56:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IkVGTExPMnhOWkMxSkRzelAwckNpWXc9PSIsInZhbHVlIjoiZjkzMXVaaTZHMEVVNU5jUjVvamlQYUs3a3B2bWZJVW15NGhxRWtmVGFYRXVlS3poYU9QT0ZYcWp2N0UxWno2a3ZGeDZ2ejlvUUdFdU4yVEpYV1U3Q1lwSGliOXVPY1NMbE42M2s3NWxORVBRSDZZYlp0eDZ4eXpBdDlyUFI4R2MiLCJtYWMiOiIxMTYyNDRhYjdlZDY3ZGY5OTUyNTQ1YTU3MmQ2ODA3ODhlNDBiOTAxMjVlMTRiZDQwMjQyYzY5NjY0NWJhYTcyIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:56:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InUrZHE5djlyVE5XbDd5cHVBdGJnYmc9PSIsInZhbHVlIjoiUSt2bkExdEw0OGxQMnFpbGw1K0hCU1VuR2RueUlEamRWdWI3dW56amV6MHEya09zQ3drL1dsVWcyTVYvbWJmVWc4MWRUTUJwamg3YlRvZnpydTZ6N0E1bWtueDNDd296OG42WWtBNmlsR0c3QUhaT2lBTHg4SWgxNCtGWHlaSzgiLCJtYWMiOiI1MDI5YTU4YzZmZWVjMzJhZjc0ZDU4YWMyMDY5MTA4ZmNmNDI4ZmNmNWI0NGZlYmY2MThmMjBjNmFlNmRhNTI1IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:56:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IkVGTExPMnhOWkMxSkRzelAwckNpWXc9PSIsInZhbHVlIjoiZjkzMXVaaTZHMEVVNU5jUjVvamlQYUs3a3B2bWZJVW15NGhxRWtmVGFYRXVlS3poYU9QT0ZYcWp2N0UxWno2a3ZGeDZ2ejlvUUdFdU4yVEpYV1U3Q1lwSGliOXVPY1NMbE42M2s3NWxORVBRSDZZYlp0eDZ4eXpBdDlyUFI4R2MiLCJtYWMiOiIxMTYyNDRhYjdlZDY3ZGY5OTUyNTQ1YTU3MmQ2ODA3ODhlNDBiOTAxMjVlMTRiZDQwMjQyYzY5NjY0NWJhYTcyIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:56:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}