<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Menambahkan item checklist baru untuk Penggantian Oli Powerpack
        DB::table('checklist_data_engine')->insert([
            'id' => 174,
            'jenis' => 'Checklist Engine Situasional',
            'sistem' => 'POWERPACK',
            'item_pemeriksaan' => 'PENGGANTIAN OLI POWERPACK',
            'waktu' => '800'
        ]);
    }

    public function down()
    {
        // Menghapus item checklist yang ditambahkan
        DB::table('checklist_data_engine')->where('id', 174)->delete();
    }
};
