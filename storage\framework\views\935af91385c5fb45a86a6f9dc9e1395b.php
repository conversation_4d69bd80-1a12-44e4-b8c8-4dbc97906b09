<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON>par<PERSON></title>
    <style>
        @page {
            margin: 1cm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 16px;
            font-weight: bold;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-table {
            width: 100%;
            margin-bottom: 10px;
        }
        .info-table td {
            padding: 2px;
            font-size: 11px;
        }
        table.data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px;
        }
        .data-table th {
            background-color: #f0f0f0;
            padding: 5px 3px;
            text-align: center;
            font-weight: bold;
            border: 0.5px solid #000;
            font-size: 10px;
        }
        .data-table td {
            padding: 4px 3px;
            border: 0.5px solid #000;
            word-wrap: break-word;
        }
        .data-table th, .data-table td {
            font-size: 9px;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10px;
            position: fixed;
            bottom: 0;
            right: 0;
            padding: 10px;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .no-border {
            border: none !important;
        }
        .page-break {
            page-break-after: always;
        }
        .narrow-column {
            width: 5%;
        }
        .medium-column {
            width: 10%;
        }
        .wide-column {
            width: 15%;
        }
    </style>
</head>
<body>
    <div class="header">
        <?php if(isset($isAdminKapal) && $isAdminKapal): ?>
            <h2>DATA SPAREPART DAN AMPRAHAN YANG ADA DI KAPAL</h2>
        <?php else: ?>
            <h2>LAPORAN STOK SPAREPART</h2>
        <?php endif; ?>
        <p><?php echo e($kapal->nama); ?></p>
    </div>

    <table class="info-table">
        <tr>
            <td style="width: 100px">Periode</td>
            <td style="width: 10px">:</td>
            <td><?php echo e($tanggal_awal); ?> s/d <?php echo e($tanggal_akhir); ?></td>
        </tr>
        <tr>
            <td>Tanggal Cetak</td>
            <td>:</td>
            <td><?php echo e(\Carbon\Carbon::now()->translatedFormat('d/m/Y H:i')); ?></td>
        </tr>
    </table>

    <table class="data-table">
        <thead>
            <tr>
                <th class="narrow-column">No</th>
                <th class="medium-column">Tanggal</th>
                <th class="wide-column">Nama Barang</th>
                <th class="medium-column">Nomor Seri</th>
                <th class="medium-column">Jenis</th>
                <th class="narrow-column">Satuan</th>
                <th class="medium-column">Transaksi</th>
                <th class="narrow-column">Jumlah</th>
                <th class="narrow-column">Stok Sebelum</th>
                <th class="narrow-column">Stok Setelah</th>
                <th>Keterangan</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td class="text-center"><?php echo e($index + 1); ?></td>
                    <td class="text-center"><?php echo e($item['tanggal']); ?></td>
                    <td><?php echo e($item['nama_barang']); ?></td>
                    <td><?php echo e($item['nomor_seri']); ?></td>
                    <td><?php echo e($item['jenis']); ?></td>
                    <td class="text-center"><?php echo e($item['satuan']); ?></td>
                    <td><?php echo e($item['jenis_transaksi']); ?></td>
                    <td class="text-right"><?php echo e($item['jumlah']); ?></td>
                    <td class="text-right"><?php echo e($item['stok_sebelum']); ?></td>
                    <td class="text-right"><?php echo e($item['stok_setelah']); ?></td>
                    <td><?php echo e($item['keterangan']); ?></td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <div class="footer">
        <p>Dicetak oleh: <?php echo e(auth()->user()->name); ?></p>
    </div>
</body>
</html> <?php /**PATH C:\laragon\www\sikapal\resources\views/stok-sparepart/pdf.blade.php ENDPATH**/ ?>