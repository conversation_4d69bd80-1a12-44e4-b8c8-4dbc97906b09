<table>
    <thead>
        <tr>
            @if(isset($isAdminKapal) && $isAdminKapal)
                <th colspan="11" style="text-align: center; font-weight: bold; font-size: 14px;">DATA SPAREPART DAN AMPRAHAN YANG ADA DI KAPAL</th>
            @else
                <th colspan="15" style="text-align: center; font-weight: bold; font-size: 14px;">DATA SPAREPART DAN AMPRAHAN</th>
            @endif
        </tr>
        <tr>
            @if(isset($isAdminKapal) && $isAdminKapal)
                <th colspan="11" style="text-align: center; font-weight: bold;">Periode: {{ Carbon\Carbon::parse($tanggal_awal)->format('d-m-Y') }} sd {{ Carbon\Carbon::parse($tanggal_akhir)->format('d-m-Y') }}</th>
            @else
                <th colspan="15" style="text-align: center; font-weight: bold;">Periode: {{ Carbon\Carbon::parse($tanggal_awal)->format('d-m-Y') }} sd {{ Carbon\Carbon::parse($tanggal_akhir)->format('d-m-Y') }}</th>
            @endif
        </tr>
        <tr>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">NO</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">NOMOR SERI / KODE</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">NAMA BARANG</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">SATUAN</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">JENIS</th>
            @if(!isset($isAdminKapal) || !$isAdminKapal)
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">STOK AWAL KANTOR</th>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">MASUK KANTOR</th>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">KELUAR KANTOR</th>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">TOTAL KANTOR</th>
            @endif
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">STOK AWAL KAPAL</th>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">MASUK KAPAL</th>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">KELUAR KAPAL</th>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">TOTAL KAPAL</th>
            <th style="background-color: #FF0000; text-align: center; font-weight: bold;">TOTAL STOK</th>
            <th style="background-color: #FF0000; text-align: center; font-weight: bold;">OPNAME</th>
        </tr>
    </thead>
    <tbody>
        @foreach($data as $index => $item)
        <tr>
            <td style="text-align: center;">{{ $index + 1 }}</td>
            <td>{{ $item['nomor_seri'] }}</td>
            <td>{{ $item['nama_barang'] }}</td>
            <td style="text-align: center;">{{ $item['satuan'] }}</td>
            <td>{{ $item['jenis'] }}</td>
            @if(!isset($isAdminKapal) || !$isAdminKapal)
                <td style="text-align: center;">{{ $item['stok_awal_kantor'] }}</td>
                <td style="text-align: center;">{{ $item['masuk_kantor'] }}</td>
                <td style="text-align: center;">{{ $item['keluar_kantor'] }}</td>
                <td style="text-align: center;">{{ $item['total_kantor'] }}</td>
            @endif
            <td style="text-align: center;">{{ $item['stok_awal_kapal'] }}</td>
            <td style="text-align: center;">{{ $item['masuk_kapal'] }}</td>
            <td style="text-align: center;">{{ $item['keluar_kapal'] }}</td>
            <td style="text-align: center;">{{ $item['total_kapal'] }}</td>
            <td style="text-align: center;">{{ $item['total_stok'] }}</td>
            <td style="text-align: center;">{{ $item['opname'] }}</td>
        </tr>
        @endforeach
    </tbody>
</table> 