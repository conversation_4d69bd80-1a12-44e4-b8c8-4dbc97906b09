{"__meta": {"id": "X18ae59f6e0c268e8c9cb5a06f44961a3", "datetime": "2025-07-20 07:15:48", "utime": **********.110478, "method": "GET", "uri": "/riwayat-pembelian?page=3", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752970547.8242, "end": **********.1105, "duration": 0.2863001823425293, "duration_str": "286ms", "measures": [{"label": "Booting", "start": 1752970547.8242, "relative_start": 0, "end": **********.018001, "relative_end": **********.018001, "duration": 0.1938011646270752, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.018016, "relative_start": 0.1938161849975586, "end": **********.110504, "relative_end": 3.814697265625e-06, "duration": 0.09248781204223633, "duration_str": "92.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25757616, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "admin_kantor.riwayat_pembelian.index", "param_count": null, "params": [], "start": **********.098378, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/riwayat_pembelian/index.blade.phpadmin_kantor.riwayat_pembelian.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Friwayat_pembelian%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": **********.103366, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.1044, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.105275, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.106256, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET riwayat-pembelian", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\PembelianKantorController@riwayat", "namespace": null, "prefix": "", "where": [], "as": "riwayat-pembelian.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=319\" onclick=\"\">app/Http/Controllers/PembelianKantorController.php:319-348</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01173, "accumulated_duration_str": "11.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.062178, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.066379, "duration": 0.010039999999999999, "duration_str": "10.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 85.592}, {"sql": "select count(*) as aggregate from `riwayat_stok_kantor` where `jenis_transaksi` = 'masuk'", "type": "query", "params": [], "bindings": ["masuk"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 344}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0795462, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:344", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=344", "ajax": false, "filename": "PembelianKantorController.php", "line": "344"}, "connection": "gema_kapal", "explain": null, "start_percent": 85.592, "width_percent": 3.836}, {"sql": "select * from `riway<PERSON>_stok_kantor` where `jenis_transaksi` = 'masuk' order by `created_at` desc limit 10 offset 20", "type": "query", "params": [], "bindings": ["masuk"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 344}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.080943, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:344", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=344", "ajax": false, "filename": "PembelianKantorController.php", "line": "344"}, "connection": "gema_kapal", "explain": null, "start_percent": 89.429, "width_percent": 5.627}, {"sql": "select * from `data_sparepart` where `data_sparepart`.`id` in (115, 116, 117, 122, 216, 219, 220, 221, 222, 223)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 344}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0842478, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PembelianKantorController.php:344", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/PembelianKantorController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\PembelianKantorController.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FPembelianKantorController.php&line=344", "ajax": false, "filename": "PembelianKantorController.php", "line": "344"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.055, "width_percent": 4.945}]}, "models": {"data": {"App\\Models\\RiwayatStokKantor": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FRiwayatStokKantor.php&line=1", "ajax": false, "filename": "RiwayatStokKantor.php", "line": "?"}}, "App\\Models\\DataSparepart": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDataSparepart.php&line=1", "ajax": false, "filename": "DataSparepart.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 21, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/riwayat-pembelian?page=3\"\n]"}, "request": {"path_info": "/riwayat-pembelian", "status_code": "<pre class=sf-dump id=sf-dump-1301349320 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1301349320\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1651831206 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651831206\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2096427208 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2096427208\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1389076386 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://sikapal.test/riwayat-pembelian?page=2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjRWVlUwNUJXaTZyRlRWYy9DVzc3S0E9PSIsInZhbHVlIjoicVVjTGs1OTRRZWpSRVJEWTZrK3FlTGRDcWtBN0NodGl6bDEwWU91T2xIY0RvUzBaeFVKWkpTTURpWGQ4ckp2bDZhYmU5bUQ5Y0RKVS9DZzFWWUpBYUt0dGtNa0pFeXBBd3J4TFlEVEM2ampnYXZ5aEs0ekh2bThLNE1CUEEzcEQiLCJtYWMiOiI0M2Y4YWU2OTFkY2MwMmI3YTI3YzU3MDI0MTc3MDIzZGVhYjMzN2FmZWNhNzVmNTVjNWFjZDU2YzQ1MzkzZGVhIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6Ik04WlBkcDhtL2lKQUt5QlVPbDV5MlE9PSIsInZhbHVlIjoicFVVMnZ5NFlxb0UyZjFTRmtrMi93TUhDWmlQUEVnUlFTRklZR2daQlhFY3hETytqMDJ3d1ZCU0tFZEFQSXM5eTQxaUhhMlpQdk82UTBjQzYxc0hXSjdzMzRlV1YyNEdvclJqejhZOFZSNSttZTUzOGRnVG11c3JiVE9xRURacGgiLCJtYWMiOiIyYzBkMDM2NjRmM2JkNTNhZWYyYWE5YTg0NDE5NWY2NWU5MmMwY2IzYjliMThjMzA4MGFhMDY3OTdlZjg1ZGNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389076386\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-221183306 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221183306\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2124458601 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 00:15:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJ6L1hUYi9vbERWY3hzdzB3SE54ZlE9PSIsInZhbHVlIjoiYjRwZnlMeWpKemZXSHZ2S010L3g2SVA0VXBMNUVzbU1WMEs1R3VKT0Z6cU5rdThBT2NlYXVzR0pIdFh4eTdOKzdPd3VqUXQzWVdlSCtVUE5ibEh5Sm42TkZlbnZKa01nZzBLTWJ3WU1FN1lIdUppTTM4Ti9zeFZqUXVhVGg4K3UiLCJtYWMiOiI1MjFiN2I4MWFjMWQ4NTUyMDkzZGM2ODlkOWI0NjllMWVjZWM5ZDM1YzE1ZmNmMzlmZjBlNmMyYjc1ZjZiNjczIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IlpTWTAxbERiK0V1c1ZlaHdYM2ZNSXc9PSIsInZhbHVlIjoiY0lsWkNyUndreHQ0V2FYaTQrekpjbUxONi9HVDRxZ05qMlNjanVFSkJCWkdnOURmb2ZISXZMZUtyeVJzQ3dzVEN1cTdmVU12WmRNUklCQ1M5aTcvM1QxS3ZYNzFNVjEvY1NnSkNWNG1rQWsxb2tYRFE5V1BOQnZQMFlFVFRUUDgiLCJtYWMiOiIzYTlmYjJiZDE4NjI5OTBhMjUxMGQ3MzlhMGFkZjMxNDBmMDc3MzM4MDQxODFlNDYxMjRhNGFhZWQ0ZTE2MGFhIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 02:15:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJ6L1hUYi9vbERWY3hzdzB3SE54ZlE9PSIsInZhbHVlIjoiYjRwZnlMeWpKemZXSHZ2S010L3g2SVA0VXBMNUVzbU1WMEs1R3VKT0Z6cU5rdThBT2NlYXVzR0pIdFh4eTdOKzdPd3VqUXQzWVdlSCtVUE5ibEh5Sm42TkZlbnZKa01nZzBLTWJ3WU1FN1lIdUppTTM4Ti9zeFZqUXVhVGg4K3UiLCJtYWMiOiI1MjFiN2I4MWFjMWQ4NTUyMDkzZGM2ODlkOWI0NjllMWVjZWM5ZDM1YzE1ZmNmMzlmZjBlNmMyYjc1ZjZiNjczIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IlpTWTAxbERiK0V1c1ZlaHdYM2ZNSXc9PSIsInZhbHVlIjoiY0lsWkNyUndreHQ0V2FYaTQrekpjbUxONi9HVDRxZ05qMlNjanVFSkJCWkdnOURmb2ZISXZMZUtyeVJzQ3dzVEN1cTdmVU12WmRNUklCQ1M5aTcvM1QxS3ZYNzFNVjEvY1NnSkNWNG1rQWsxb2tYRFE5V1BOQnZQMFlFVFRUUDgiLCJtYWMiOiIzYTlmYjJiZDE4NjI5OTBhMjUxMGQ3MzlhMGFkZjMxNDBmMDc3MzM4MDQxODFlNDYxMjRhNGFhZWQ0ZTE2MGFhIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 02:15:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124458601\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://sikapal.test/riwayat-pembelian?page=3</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}