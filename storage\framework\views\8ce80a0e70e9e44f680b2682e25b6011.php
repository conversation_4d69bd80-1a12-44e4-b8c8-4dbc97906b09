<table>
    <thead>
        <tr>
            <?php if(isset($isAdminKapal) && $isAdminKapal): ?>
                <th colspan="11" style="text-align: center; font-weight: bold; font-size: 14px;">DATA SPAREPART DAN AMPRAHAN YANG ADA DI KAPAL</th>
            <?php else: ?>
                <th colspan="15" style="text-align: center; font-weight: bold; font-size: 14px;">DATA SPAREPART DAN AMPRAHAN</th>
            <?php endif; ?>
        </tr>
        <tr>
            <?php if(isset($isAdminKapal) && $isAdminKapal): ?>
                <th colspan="11" style="text-align: center; font-weight: bold;">Periode: <?php echo e(Carbon\Carbon::parse($tanggal_awal)->format('d-m-Y')); ?> sd <?php echo e(Carbon\Carbon::parse($tanggal_akhir)->format('d-m-Y')); ?></th>
            <?php else: ?>
                <th colspan="15" style="text-align: center; font-weight: bold;">Periode: <?php echo e(Carbon\Carbon::parse($tanggal_awal)->format('d-m-Y')); ?> sd <?php echo e(Carbon\Carbon::parse($tanggal_akhir)->format('d-m-Y')); ?></th>
            <?php endif; ?>
        </tr>
        <tr>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">NO</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">NOMOR SERI / KODE</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">NAMA BARANG</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">SATUAN</th>
            <th style="background-color: #E2EFDA; text-align: center; font-weight: bold;">JENIS</th>
            <?php if(!isset($isAdminKapal) || !$isAdminKapal): ?>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">STOK AWAL KANTOR</th>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">MASUK KANTOR</th>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">KELUAR KANTOR</th>
                <th style="background-color: #FFFF00; text-align: center; font-weight: bold;">TOTAL KANTOR</th>
            <?php endif; ?>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">STOK AWAL KAPAL</th>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">MASUK KAPAL</th>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">KELUAR KAPAL</th>
            <th style="background-color: #00FFFF; text-align: center; font-weight: bold;">TOTAL KAPAL</th>
            <th style="background-color: #FF0000; text-align: center; font-weight: bold;">TOTAL STOK</th>
            <th style="background-color: #FF0000; text-align: center; font-weight: bold;">OPNAME</th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td style="text-align: center;"><?php echo e($index + 1); ?></td>
            <td><?php echo e($item['nomor_seri']); ?></td>
            <td><?php echo e($item['nama_barang']); ?></td>
            <td style="text-align: center;"><?php echo e($item['satuan']); ?></td>
            <td><?php echo e($item['jenis']); ?></td>
            <?php if(!isset($isAdminKapal) || !$isAdminKapal): ?>
                <td style="text-align: center;"><?php echo e($item['stok_awal_kantor']); ?></td>
                <td style="text-align: center;"><?php echo e($item['masuk_kantor']); ?></td>
                <td style="text-align: center;"><?php echo e($item['keluar_kantor']); ?></td>
                <td style="text-align: center;"><?php echo e($item['total_kantor']); ?></td>
            <?php endif; ?>
            <td style="text-align: center;"><?php echo e($item['stok_awal_kapal']); ?></td>
            <td style="text-align: center;"><?php echo e($item['masuk_kapal']); ?></td>
            <td style="text-align: center;"><?php echo e($item['keluar_kapal']); ?></td>
            <td style="text-align: center;"><?php echo e($item['total_kapal']); ?></td>
            <td style="text-align: center;"><?php echo e($item['total_stok']); ?></td>
            <td style="text-align: center;"><?php echo e($item['opname']); ?></td>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table> <?php /**PATH C:\laragon\www\sikapal\resources\views/stok-sparepart/export.blade.php ENDPATH**/ ?>