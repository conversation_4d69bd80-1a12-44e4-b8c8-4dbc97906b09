<?php

namespace App\Http\Controllers;

use App\Models\Kapal;
use App\Models\ChecklistEngine;
use App\Models\ChecklistDataEngine;
use Illuminate\Http\Request;
use App\Helpers\HashIdHelper;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\HasilPemeriksaanEngine;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ChecklistEngineController extends Controller
{
    public function index($hashedId)
    {
        try {
            $id = HashIdHelper::decode($hashedId);
            $kapal = Kapal::findOrFail($id);
            
            // Tambahkan log untuk debugging
            \Log::info('Kapal:', ['id' => $id, 'kapal' => $kapal]);
            
            // Pastikan kapal ditemukan
            if (!$kapal) {
                return redirect()->route('dashboard')->with('error', 'Kapal tidak ditemukan');
            }
            
            // Pastikan kapal memiliki jenis_kapal
            if (!$kapal->jenis_kapal) {
                return redirect()->route('dashboard')->with('error', 'Jenis kapal tidak ditemukan');
            }
            
            // Hanya untuk tugboat
            if ($kapal->jenis_kapal_id != 1) {
                return redirect()->route('dashboard')->with('error', 'Checklist engine hanya tersedia untuk kapal tugboat');
            }
            
            // Ambil histori perjalanan terbaru
            $latestHistory = DB::table('histori_perjalanan_kapal')
                ->where('kapal_id', $id)
                ->orderBy('created_at', 'desc')
                ->first();

            // Ambil hasil pemeriksaan engine untuk notifikasi (hanya dari history terakhir)
            $hasilPemeriksaanTerbaru = DB::table('hasil_pemeriksaan_engine as hpe')
                ->select('hpe.*', 'cde.item_pemeriksaan')
                ->leftJoin('checklist_data_engine as cde', 'hpe.item_id', '=', 'cde.id')
                ->where('hpe.kapal_id', $id)
                ->where('hpe.history_id', $latestHistory ? $latestHistory->id : 0)
                ->get();

            // Ambil semua hasil pemeriksaan untuk ditampilkan di tabel
            $hasilPemeriksaan = DB::table('hasil_pemeriksaan_engine as hpe')
                ->select('hpe.*', 'cde.item_pemeriksaan', 'hpk.tujuan', 'hpk.rh_me_ps', 'hpk.rh_me_sb')
                ->leftJoin('checklist_data_engine as cde', 'hpe.item_id', '=', 'cde.id')
                ->leftJoin('histori_perjalanan_kapal as hpk', 'hpe.history_id', '=', 'hpk.id')
                ->where('hpe.kapal_id', $id)
                ->when(request()->search, function($query) {
                    $search = request()->search;
                    return $query->where(function($q) use ($search) {
                        $q->where('cde.item_pemeriksaan', 'like', "%{$search}%")
                          ->orWhere('hpe.keterangan', 'like', "%{$search}%")
                          ->orWhere('hpe.kondisi', 'like', "%{$search}%")
                          ->orWhere('hpk.tujuan', 'like', "%{$search}%");
                    });
                })
                ->when(request()->sort_by && request()->sort_order, function($query) {
                    $sortBy = request()->sort_by;
                    $sortOrder = request()->sort_order;
                    
                    // Mapping kolom sort
                    $sortableColumns = [
                        'tanggal' => 'hpe.tanggal_pemeriksaan',
                        'item' => 'cde.item_pemeriksaan',
                        'kondisi' => 'hpe.kondisi',
                        'keterangan' => 'hpe.keterangan'
                    ];
                    
                    if (isset($sortableColumns[$sortBy])) {
                        return $query->orderBy($sortableColumns[$sortBy], $sortOrder);
                    }
                    
                    return $query;
                }, function($query) {
                    return $query->orderBy('hpe.tanggal_pemeriksaan', 'desc')
                                ->orderBy('hpe.created_at', 'desc');
                })
                ->paginate(10)
                ->appends(request()->query());

            $checklists = ChecklistEngine::where('kapal_id', $id)
                                       ->orderBy('tanggal', 'desc')
                                       ->orderBy('created_at', 'desc')
                                       ->paginate(10);
            
            $engineItems = $this->getEngineItems();
            
            // Ambil notifikasi checklist yang harus dilakukan
            $notifications = [];
            if ($kapal) {
                $notifications = ChecklistDataEngine::getNotifications(
                    $kapal->rh_me_ps ?? 0,
                    $kapal->rh_me_sb ?? 0,
                    $id // kirim kapal_id untuk logika yang lebih akurat
                );

                // Tidak perlu filter manual lagi karena sudah ditangani di dalam getNotifications
            }

            // Ambil daftar tujuan unik dari histori perjalanan kapal
            $tujuanList = DB::table('histori_perjalanan_kapal')
                ->where('kapal_id', $id)
                ->whereNotNull('tujuan')
                ->distinct()
                ->pluck('tujuan')
                ->toArray();

            return view('checklist-engine.index', compact('kapal', 'checklists', 'engineItems', 'notifications', 'hasilPemeriksaan', 'latestHistory', 'tujuanList'));
        } catch (\Exception $e) {
            \Log::error('Error in ChecklistEngineController@index:', [
                'hashedId' => $hashedId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('dashboard')->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    public function store(Request $request, $hashedId)
    {
        try {
            $id = HashIdHelper::decode($hashedId);
            
            $request->validate([
                'tanggal' => 'required|date',
                'hasil_pemeriksaan' => 'required|array',
                'keterangan' => 'nullable|string'
            ]);

            // Format hasil pemeriksaan dengan nama bagian mesin
            $hasilPemeriksaan = [];
            $engineItems = $this->getEngineItems();

            // Debug log untuk melihat struktur engineItems
            \Log::info('Engine Items Structure:', $engineItems);

            foreach ($request->hasil_pemeriksaan as $key => $item) {
                // Tentukan nama bagian mesin dan group key berdasarkan prefix key
                $engineName = '';
                $groupKey = '';
                
                if (strpos($key, 'generator_starboard_') !== false) {
                    $engineName = 'Generator Engine Starboardside (YANMAR 4TNV98-GGE)';
                    $groupKey = 'generator_engine_starboardside';
                } elseif (strpos($key, 'starboard_') !== false) {
                    $engineName = 'Main Engine Starboardside (MITSUBISHI S6R2-MTK3L S6R2-MPTK3)';
                    $groupKey = 'main_engine_starboardside';
                } elseif (strpos($key, 'generator_sb_') !== false) {
                    $engineName = 'Emergency Generator Engine (YANMAR 4TNV98-GGE)';
                    $groupKey = 'emergency_generator_engine';
                } elseif (strpos($key, 'gearbox_sb_') !== false) {
                    $engineName = 'Gearbox Starboardside';
                    $groupKey = 'gearbox_starboardside';
                } elseif (strpos($key, 'fire_system_') !== false) {
                    $engineName = 'Fire System';
                    $groupKey = 'fire_system';
                } elseif (strpos($key, 'emergency_fire_pump_') !== false) {
                    $engineName = 'Emergency Fire Pump';
                    $groupKey = 'emergency_fire_pump';
                } elseif (strpos($key, 'fire_pump_') !== false) {
                    $engineName = 'Fire Pump';
                    $groupKey = 'fire_pump';
                } elseif (strpos($key, 'ballast_system_') !== false) {
                    $engineName = 'Ballast System Port & STBD';
                    $groupKey = 'ballast_system';
                } elseif (strpos($key, 'bilge_gs_') !== false) {
                    $engineName = 'Bilge and GS System';
                    $groupKey = 'bilge_gs_system';
                } elseif (strpos($key, 'emergency_steering_') !== false) {
                    $engineName = 'Emergency Steering';
                    $groupKey = 'emergency_steering';
                } elseif (strpos($key, 'control_consol_') !== false) {
                    $engineName = 'Control Consol';
                    $groupKey = 'control_consol';
                } elseif (strpos($key, 'cleanliness_er_') !== false) {
                    $engineName = 'Cleanliness of E/R';
                    $groupKey = 'cleanliness_er';
                } elseif (strpos($key, 'quick_closing_valves_') !== false) {
                    $engineName = 'Quick Closing Valves';
                    $groupKey = 'quick_closing_valves';
                } elseif (strpos($key, 'generator_') !== false) {
                    $engineName = 'Generator Engine Portside (YANMAR 4TNV98-GGE)';
                    $groupKey = 'generator_engine_portside';
                } else {
                    $engineName = 'Main Engine Portside (MITSUBISHI S6R2-MTK3)';
                    $groupKey = 'main_engine_portside';
                }

                // Inisialisasi group jika belum ada
                if (!isset($hasilPemeriksaan[$groupKey])) {
                    $hasilPemeriksaan[$groupKey] = [
                        'nama' => $engineName,
                        'items' => []
                    ];
                }

                // Cari informasi item dari engineItems
                $itemInfo = null;
                if ($groupKey === 'emergency_generator_engine') {
                    // Untuk Emergency Generator Engine
                    $baseKey = str_replace('generator_sb_', '', $key);
                    $category = '';
                    
                    // Cari kategori yang sesuai
                    foreach ($engineItems[$groupKey] as $cat => $items) {
                        if (isset($items[$baseKey])) {
                            $category = $cat;
                            $itemInfo = $items[$baseKey];
                            break;
                        }
                    }
                    
                    // Jika kategori ditemukan, tambahkan ke struktur hasil
                    if ($category && !isset($hasilPemeriksaan[$groupKey]['categories'][$category])) {
                        $hasilPemeriksaan[$groupKey]['categories'][$category] = [];
                    }
                    
                    // Tambahkan item ke kategori yang sesuai
                    if ($category) {
                        $hasilPemeriksaan[$groupKey]['categories'][$category][$baseKey] = [
                            'status' => $item['status'],
                            'keterangan' => $item['keterangan'] ?? null,
                            'nama' => $itemInfo['nama'] ?? null,
                            'interval' => $itemInfo['interval'] ?? null,
                            'satuan' => $itemInfo['satuan'] ?? null,
                            'running_hours' => $item['running_hours'] ?? null,
                            'next_replacement' => $item['next_replacement'] ?? null
                        ];
                    }
                } elseif ($groupKey === 'generator_engine_starboardside' || $groupKey === 'generator_engine_portside') {
                    // Untuk generator engine yang memiliki sub-kategori
                    foreach ($engineItems[$groupKey] as $category => $items) {
                        foreach ($items as $itemKey => $itemData) {
                            if (strpos($key, $itemKey) !== false) {
                                $itemInfo = $itemData;
                                break 2;
                            }
                        }
                    }

                    // Tambahkan item ke hasil pemeriksaan
                    $hasilPemeriksaan[$groupKey]['items'][$key] = [
                        'status' => $item['status'],
                        'keterangan' => $item['keterangan'] ?? null,
                        'nama' => $itemInfo['nama'] ?? null,
                        'interval' => $itemInfo['interval'] ?? null,
                        'satuan' => $itemInfo['satuan'] ?? null,
                        'running_hours' => $item['running_hours'] ?? null,
                        'next_replacement' => $item['next_replacement'] ?? null
                    ];
                } elseif ($groupKey === 'checklist_engine_situasional') {
                    // Untuk checklist engine situasional
                    foreach ($engineItems[$groupKey] as $itemKey => $itemData) {
                        if (strpos($key, $itemKey) !== false) {
                            $itemInfo = $itemData;
                            break;
                        }
                    }

                    // Tambahkan item ke hasil pemeriksaan
                    $hasilPemeriksaan[$groupKey]['items'][$key] = [
                        'status' => $item['status'],
                        'keterangan' => $item['keterangan'] ?? null,
                        'nama' => $itemInfo['nama'] ?? null,
                        'interval' => $itemInfo['interval'] ?? null,
                        'satuan' => $itemInfo['satuan'] ?? null,
                        'running_hours' => $item['running_hours'] ?? null,
                        'next_replacement' => $item['next_replacement'] ?? null
                    ];
                } else {
                    // Untuk sistem lain yang tidak memiliki sub-kategori
                    foreach ($engineItems[$groupKey] as $itemKey => $itemData) {
                        if (strpos($key, $itemKey) !== false) {
                            $itemInfo = $itemData;
                            break;
                        }
                    }
                    
                    // Tambahkan item ke hasil pemeriksaan
                    $hasilPemeriksaan[$groupKey]['items'][$key] = [
                        'status' => $item['status'],
                        'keterangan' => $item['keterangan'] ?? null,
                        'nama' => $itemInfo['nama'] ?? null,
                        'interval' => $itemInfo['interval'] ?? null,
                        'satuan' => $itemInfo['satuan'] ?? null,
                        'running_hours' => $item['running_hours'] ?? null,
                        'next_replacement' => $item['next_replacement'] ?? null
                    ];
                }
            }

            // Debug log untuk melihat hasil akhir
            \Log::info('Final hasil pemeriksaan:', $hasilPemeriksaan);

            ChecklistEngine::create([
                'kapal_id' => $id,
                'tanggal' => $request->tanggal,
                'hasil_pemeriksaan' => $hasilPemeriksaan,
                'keterangan' => $request->keterangan
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Checklist berhasil disimpan'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in store checklist:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan checklist: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($hashedId, $checklistId)
    {
        try {
            $checklist = ChecklistEngine::findOrFail($checklistId);
            $checklist->delete();

            return response()->json([
                'success' => true,
                'message' => 'Checklist berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus checklist: ' . $e->getMessage()
            ], 500);
        }
    }

    public function updatePerjalanan(Request $request, $hashedId)
    {
        try {
            $id = HashIdHelper::decode($hashedId);
            $kapal = Kapal::findOrFail($id);

            $request->validate([
                'rh_me_ps' => 'required|numeric',
                'rh_me_sb' => 'required|numeric',
                'tujuan' => 'required|string'
            ]);

            // Update data kapal dengan menambahkan nilai baru ke nilai yang sudah ada
            $kapal->update([
                'rh_me_ps' => $kapal->rh_me_ps + $request->rh_me_ps,
                'rh_me_sb' => $kapal->rh_me_sb + $request->rh_me_sb,
                'tujuan' => $request->tujuan
            ]);

            // Simpan histori perjalanan dengan nilai yang baru diinput
            $kapal->historiPerjalanan()->create([
                'rh_me_ps' => $request->rh_me_ps,
                'rh_me_sb' => $request->rh_me_sb,
                'tujuan' => $request->tujuan
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Data perjalanan kapal berhasil diupdate'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupdate data perjalanan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteHistori($hashedId, $historiId)
    {
        try {
            $id = HashIdHelper::decode($hashedId);
            $kapal = Kapal::findOrFail($id);
            
            // Ambil data histori yang akan dihapus
            $histori = $kapal->historiPerjalanan()->findOrFail($historiId);
            
            // Kurangi running hours di tabel kapal
            $kapal->update([
                'rh_me_ps' => $kapal->rh_me_ps - $histori->rh_me_ps,
                'rh_me_sb' => $kapal->rh_me_sb - $histori->rh_me_sb
            ]);
            
            // Hapus hasil pemeriksaan engine terkait terlebih dahulu
            DB::table('hasil_pemeriksaan_engine')
                ->where('history_id', $historiId)
                ->delete();
            
            // Hapus histori perjalanan
            $histori->delete();

            return response()->json([
                'success' => true,
                'message' => 'Histori perjalanan berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus histori perjalanan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function detail($id)
    {
        try {
            // Ambil data hasil pemeriksaan dengan join ke checklist_data_engine
            $hasilPemeriksaan = DB::table('hasil_pemeriksaan_engine as hpe')
                ->select(
                    'hpe.*',
                    'cde.item_pemeriksaan',
                    'hpk.rh_me_ps',
                    'hpk.rh_me_sb'
                )
                ->leftJoin('checklist_data_engine as cde', 'hpe.item_id', '=', 'cde.id')
                ->leftJoin('histori_perjalanan_kapal as hpk', 'hpe.history_id', '=', 'hpk.id')
                ->where('hpe.id', $id)
                ->first();

            if (!$hasilPemeriksaan) {
                throw new \Exception('Data pemeriksaan tidak ditemukan');
            }

            // Format tanggal
            $hasilPemeriksaan->tanggal_pemeriksaan = \Carbon\Carbon::parse($hasilPemeriksaan->tanggal_pemeriksaan)
                ->format('Y-m-d H:i:s');

            return response()->json([
                'success' => true,
                'data' => $hasilPemeriksaan
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in ChecklistEngineController@detail:', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil detail: ' . $e->getMessage()
            ], 500);
        }
    }

    public function download(Request $request, $id)
    {
        try {
            $checklist = ChecklistEngine::with('kapal')->findOrFail($id);
            
            // Definisikan nama lengkap untuk setiap grup
            $groupNames = [
                'main_engine_portside' => 'Main Engine Portside (MITSUBISHI S6R2-MTK3)',
                'generator_engine_portside' => 'Generator Engine Portside (YANMAR 4TNV98-GGE)',
                'main_engine_starboardside' => 'Main Engine Starboardside (MITSUBISHI S6R2-MTK3L S6R2-MPTK3)',
                'generator_engine_starboardside' => 'Generator Engine Starboardside (YANMAR 4TNV98-GGE)',
                'emergency_generator_engine' => 'Emergency Generator Engine (YANMAR 4TNV98-GGE)',
                'gearbox_starboardside' => 'Gearbox Starboardside',
                'fire_system' => 'Fire System',
                'fire_pump' => 'Fire Pump',
                'emergency_fire_pump' => 'Emergency Fire Pump',
                'ballast_system' => 'Ballast System Port & STBD',
                'bilge_gs_system' => 'Bilge and GS System',
                'emergency_steering' => 'Emergency Steering',
                'control_consol' => 'Control Consol',
                'cleanliness_er' => 'Cleanliness of E/R',
                'quick_closing_valves' => 'Quick Closing Valves',
                'checklist_engine_situasional' => 'Checklist Engine Situasional'
            ];

            // Format hasil pemeriksaan untuk PDF
            $formattedItems = [];
            foreach ($checklist->hasil_pemeriksaan as $groupKey => $group) {
                $formattedItems[$groupKey] = [
                    'nama' => $groupNames[$groupKey] ?? ucfirst(str_replace('_', ' ', $groupKey)),
                    'items' => []
                ];
                
                if (isset($group['items'])) {
                    foreach ($group['items'] as $itemKey => $item) {
                        // Dapatkan informasi item dari engineItems
                        $itemInfo = $this->getItemInfo($groupKey, $itemKey);
                        
                        // Debug log
                        \Log::info('Item Info:', [
                            'groupKey' => $groupKey,
                            'itemKey' => $itemKey,
                            'itemInfo' => $itemInfo
                        ]);
                        
                        // Tentukan sistem berdasarkan kategori
                        $sistem = '';
                        if (strpos($groupKey, 'engine') !== false) {
                            $sistem = 'Engine System';
                        } elseif (strpos($groupKey, 'gearbox') !== false) {
                            $sistem = 'Transmission System';
                        } elseif (strpos($groupKey, 'fire') !== false) {
                            $sistem = 'Fire Fighting System';
                        } elseif (strpos($groupKey, 'ballast') !== false || strpos($groupKey, 'bilge') !== false) {
                            $sistem = 'Pumping System';
                        } else {
                            $sistem = 'General System';
                        }
                        
                        $formattedItems[$groupKey]['items'][] = [
                            'nama' => $itemInfo['nama'],  // Gunakan nama dari itemInfo
                            'sistem' => $sistem,
                            'interval' => $itemInfo['interval'],
                            'satuan' => $itemInfo['satuan'],
                            'status' => $item['status'] ?? '-',
                            'keterangan' => $item['keterangan'] ?? '-'
                        ];
                    }
                }
            }

            $data = [
                'checklist' => $checklist,
                'items' => $formattedItems,
                'tempat_pemeriksaan' => $request->tempat_pemeriksaan,
                'tanggal_pemeriksaan' => $request->tanggal_pemeriksaan,
                'yang_memeriksa' => $request->yang_memeriksa,
                'kkm' => $request->kkm,
                'masinis_2' => $request->masinis_2,
                'masinis_3' => $request->masinis_3,
                'running_hour_me_ps' => $request->running_hour_me_ps,
                'running_hour_me_sb' => $request->running_hour_me_sb
            ];

            $pdf = Pdf::loadView('checklist-engine.pdf.detail', $data);
            
            $filename = 'checklist-engine-' . $checklist->kapal->nama . '-' . 
                        Carbon::parse($checklist->tanggal)->format('Y-m-d') . '.pdf';

            return $pdf->download($filename);

        } catch (\Exception $e) {
            \Log::error('Error in download:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengunduh PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    // Fungsi helper untuk mendapatkan informasi item
    private function getItemInfo($groupKey, $itemKey)
    {
        $engineItems = $this->getEngineItems();
        
        // Debug log
        \Log::info('Getting Item Info:', [
            'groupKey' => $groupKey,
            'itemKey' => $itemKey,
            'engineItems' => $engineItems[$groupKey] ?? null
        ]);

        // Untuk item yang memiliki sub-kategori
        if (isset($engineItems[$groupKey])) {
            // Jika langsung array items
            if (isset($engineItems[$groupKey][$itemKey])) {
                return $engineItems[$groupKey][$itemKey];
            }
            
            // Jika ada sub-kategori
            if (is_array($engineItems[$groupKey])) {
                foreach ($engineItems[$groupKey] as $category => $items) {
                    if (isset($items[$itemKey])) {
                        return $items[$itemKey];
                    }
                }
            }
        }
        
        // Log item yang tidak ditemukan
        \Log::warning('Item Info Not Found:', [
            'groupKey' => $groupKey,
            'itemKey' => $itemKey
        ]);
        
        // Ubah format itemKey menjadi nama yang lebih readable
        $readableName = ucwords(str_replace('_', ' ', $itemKey));
        
        return [
            'nama' => $readableName,
            'interval' => '-',
            'satuan' => '-'
        ];
    }

    private function getEngineItems()
    {
        return [
            'main_engine_portside' => [
                'ganti_oli' => [
                    'nama' => 'Ganti Oli',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'ganti_filter_oli' => [
                    'nama' => 'Ganti Filter Oli',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'ganti_filter_oli_bypass' => [
                    'nama' => 'Ganti Filter Oli Bypass',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'menguras_tangki_harian_bahan_bakar' => [
                    'nama' => 'Menguras Tangki Harian Bahan Bakar',
                    'interval' => 1600,
                    'satuan' => 'jam'
                ],
                'ganti_gear_oil_gearbox' => [
                    'nama' => 'Ganti Gear Oil (Gearbox)',
                    'interval' => 800,
                    'satuan' => 'jam'
                ],
                'membersihkan_saringan_gearbox' => [
                    'nama' => 'Membersihkan Saringan Gearbox',
                    'interval' => 800,
                    'satuan' => 'jam'
                ],
                'pemeriksaan_penyesuaian_celah_katup' => [
                    'nama' => 'Pemeriksaan dan Penyesuaian Celah Katup',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'mengencangkan_baut_dan_mur' => [
                    'nama' => 'Mengencangkan Baut dan Mur Bagian Luar Mesin',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'memeriksa_oli_filter_oli_bypass' => [
                    'nama' => 'Memeriksa Oli Mesin, Filter Oli dan Filter Oli Bypass',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'memeriksa_vbelt' => [
                    'nama' => 'Memeriksa V-Belt dan Penyesuaian Kekencangan V-Belt',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'membersihkan_precleaner' => [
                    'nama' => 'Membersihkan, Memeriksa dan Mengganti Pre-Cleaner',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'membersihkan_filter_kasa' => [
                    'nama' => 'Membersihkan Filter Kasa',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'mengganti_oli_governer' => [
                    'nama' => 'Mengganti Oli Governer',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'memeriksa_zinc_rods' => [
                    'nama' => 'Memeriksa dan Mengganti Zinc Rods',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'mengganti_elemen_separator' => [
                    'nama' => 'Mengganti Elemen Separator',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_impeller' => [
                    'nama' => 'Memeriksa Impeller Pompa Laut',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'mengganti_filter_bahan_bakar' => [
                    'nama' => 'Mengganti Filter Bahan Bakar',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'mengganti_oli_transmisi' => [
                    'nama' => 'Mengganti Oli Transmisi dan Membersihkan Saringan Transmisi',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'mengencangkan_baut_dan_mur_mesin' => [
                    'nama' => 'Mengencangkan Baut dan Mur',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_nozzle_injector' => [
                    'nama' => 'Memeriksa dan Menyesuaikan Nozzle Injector Bahan Bakar',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_celah_katup' => [
                    'nama' => 'Memeriksa dan Menyesuaikan Celah Katup',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_waktu_injeksi' => [
                    'nama' => 'Memeriksa Waktu Injeksi Bahan Bakar',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_kebocoran_joint' => [
                    'nama' => 'Memeriksa Kebocoran Ball Joint pada Kontrol Bahan Bakar',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_turbocharger' => [
                    'nama' => 'Memeriksa Turbocharger',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_heat_exchanger' => [
                    'nama' => 'Memeriksa Heat Exchanger',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_damper' => [
                    'nama' => 'Memeriksa Damper/Peredam',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_intercooler' => [
                    'nama' => 'Memeriksa Intercooler',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_starter' => [
                    'nama' => 'Memeriksa Starter',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_alternator' => [
                    'nama' => 'Memeriksa Alternator',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'membersihkan_oil_cooler' => [
                    'nama' => 'Membersihkan Oil Cooler Transmisi',
                    'interval' => 5000,
                    'satuan' => 'jam'
                ],
                'mengganti_damper' => [
                    'nama' => 'Mengganti Damper/Peredam',
                    'interval' => 8000,
                    'satuan' => 'jam'
                ],
                'mengganti_air_pendingin' => [
                    'nama' => 'Mengganti Air Pendingin (Air Tawar)',
                    'interval' => 2,
                    'satuan' => 'tahun',
                    'tipe_input' => 'date'
                ],
                'mengganti_roller_bearing' => [
                    'nama' => 'Mengganti Roller Bearing Transmisi, Bushing Pompa Oli dan Kopling Mesin',
                    'interval' => 4,
                    'satuan' => 'tahun',
                    'tipe_input' => 'date'
                ],
                'memeriksa_kebocoran_sistem' => [
                    'nama' => 'Memeriksa Kebocoran Sistem Bahan Bakar',
                    'interval' => null,
                    'satuan' => 'jam'
                ],
                'memeriksa_baterai' => [
                    'nama' => 'Memeriksa Baterai / Memeriksa Kecukupan Air Baterai',
                    'interval' => 1,
                    'satuan' => 'harian',
                    'tipe_input' => 'date'
                ]
            ],
            'main_engine_starboardside' => [
                'ganti_oli' => [
                    'nama' => 'Ganti Oli',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'ganti_filter_oli' => [
                    'nama' => 'Ganti Filter Oli',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'ganti_filter_oli_bypass' => [
                    'nama' => 'Ganti Filter Oli Bypass',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'menguras_tangki_harian_bahan_bakar' => [
                    'nama' => 'Menguras Tangki Harian Bahan Bakar',
                    'interval' => 1600,
                    'satuan' => 'jam'
                ],
                'ganti_gear_oil_gearbox' => [
                    'nama' => 'Ganti Gear Oil (Gearbox)',
                    'interval' => 800,
                    'satuan' => 'jam'
                ],
                'membersihkan_saringan_gearbox' => [
                    'nama' => 'Membersihkan Saringan Gearbox',
                    'interval' => 800,
                    'satuan' => 'jam'
                ],
                'pemeriksaan_penyesuaian_celah_katup' => [
                    'nama' => 'Pemeriksaan dan Penyesuaian Celah Katup',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'mengencangkan_baut_dan_mur' => [
                    'nama' => 'Mengencangkan Baut dan Mur Bagian Luar Mesin',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'memeriksa_oli_filter_oli_bypass' => [
                    'nama' => 'Memeriksa Oli Mesin, Filter Oli dan Filter Oli Bypass',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'memeriksa_vbelt' => [
                    'nama' => 'Memeriksa V-Belt dan Penyesuaian Kekencangan V-Belt',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'membersihkan_precleaner' => [
                    'nama' => 'Membersihkan, Memeriksa dan Mengganti Pre-Cleaner',
                    'interval' => 250,
                    'satuan' => 'jam'
                ],
                'membersihkan_filter_kasa' => [
                    'nama' => 'Membersihkan Filter Kasa',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'mengganti_oli_governer' => [
                    'nama' => 'Mengganti Oli Governer',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'memeriksa_zinc_rods' => [
                    'nama' => 'Memeriksa dan Mengganti Zinc Rods',
                    'interval' => 400,
                    'satuan' => 'jam'
                ],
                'mengganti_elemen_separator' => [
                    'nama' => 'Mengganti Elemen Separator',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_impeller' => [
                    'nama' => 'Memeriksa Impeller Pompa Laut',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'mengganti_filter_bahan_bakar' => [
                    'nama' => 'Mengganti Filter Bahan Bakar',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'mengganti_oli_transmisi' => [
                    'nama' => 'Mengganti Oli Transmisi dan Membersihkan Saringan Transmisi',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'mengencangkan_baut_dan_mur_mesin' => [
                    'nama' => 'Mengencangkan Baut dan Mur',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_nozzle_injector' => [
                    'nama' => 'Memeriksa dan Menyesuaikan Nozzle Injector Bahan Bakar',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_celah_katup' => [
                    'nama' => 'Memeriksa dan Menyesuaikan Celah Katup',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_waktu_injeksi' => [
                    'nama' => 'Memeriksa Waktu Injeksi Bahan Bakar',
                    'interval' => 1000,
                    'satuan' => 'jam'
                ],
                'memeriksa_kebocoran_joint' => [
                    'nama' => 'Memeriksa Kebocoran Ball Joint pada Kontrol Bahan Bakar',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_turbocharger' => [
                    'nama' => 'Memeriksa Turbocharger',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_heat_exchanger' => [
                    'nama' => 'Memeriksa Heat Exchanger',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_damper' => [
                    'nama' => 'Memeriksa Damper/Peredam',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_intercooler' => [
                    'nama' => 'Memeriksa Intercooler',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_starter' => [
                    'nama' => 'Memeriksa Starter',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'memeriksa_alternator' => [
                    'nama' => 'Memeriksa Alternator',
                    'interval' => 2000,
                    'satuan' => 'jam'
                ],
                'membersihkan_oil_cooler' => [
                    'nama' => 'Membersihkan Oil Cooler Transmisi',
                    'interval' => 5000,
                    'satuan' => 'jam'
                ],
                'mengganti_damper' => [
                    'nama' => 'Mengganti Damper/Peredam',
                    'interval' => 8000,
                    'satuan' => 'jam'
                ],
                'mengganti_air_pendingin' => [
                    'nama' => 'Mengganti Air Pendingin (Air Tawar)',
                    'interval' => 2,
                    'satuan' => 'tahun',
                    'tipe_input' => 'date'
                ],
                'mengganti_roller_bearing' => [
                    'nama' => 'Mengganti Roller Bearing Transmisi, Bushing Pompa Oli dan Kopling Mesin',
                    'interval' => 4,
                    'satuan' => 'tahun',
                    'tipe_input' => 'date'
                ],
                'memeriksa_kebocoran_sistem' => [
                    'nama' => 'Memeriksa Kebocoran Sistem Bahan Bakar',
                    'interval' => null,
                    'satuan' => 'jam'
                ],
                'memeriksa_baterai' => [
                    'nama' => 'Memeriksa Baterai / Memeriksa Kecukupan Air Baterai',
                    'interval' => 1,
                    'satuan' => 'harian',
                    'tipe_input' => 'date'
                ]
            ],
            'generator_engine_portside' => [
                'cooling_system' => [
                    'cek_coolant' => [
                        'nama' => 'Cek dan Isi Ulang Cairan Pendingin Mesin / Water Coolant',
                        'interval' => 1,
                        'satuan' => 'harian'
                    ],
                    'cek_bersihkan_core_heat_exchanger' => [
                        'nama' => 'Cek dan Bersihkan Core Heat Exchanger',
                        'interval' => 50,
                        'satuan' => 'jam',
                    ],
                    'cek_sesuaikan_vbelt_fresh_water_pump' => [
                        'nama' => 'Cek dan Sesuaikan V-Belt Fresh Water Pump & Alternator',
                        'interval' => 50,
                        'satuan' => 'jam',
                    ],
                    'cek_sesuaikan_vbelt_fresh_water_pump_250' => [
                        'nama' => 'Cek dan Sesuaikan V-Belt Fresh Water Pump & Alternator',
                        'interval' => 250,
                        'satuan' => 'jam',
                    ],
                    'kuras_isi_ulang_sistem_pendingin' => [
                        'nama' => 'Kuras dan Isi Ulang Sistem Pendingin dengan Pendingin Baru',
                        'interval' => 2000,
                        'satuan' => 'jam',
                    ]
                ],
                'cylinder_head' => [
                    'sesuaikan_jarak_bebas_katup' => [
                        'nama' => 'Sesuaikan Jarak Bebas Katup HSAP & Buang',
                        'interval' => 1000,
                        'satuan' => 'jam',
                    ]
                ],
                'electrical_equipment' => [
                    'periksa_indikator' => [
                        'nama' => 'Periksa Indikator',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'periksa_baterai' => [
                        'nama' => 'Periksa Baterai & Level Air Accu',
                        'interval' => 50,
                        'satuan' => 'jam',
                    ]
                ],
                'engine_oil' => [
                    'periksa_level_oli' => [
                        'nama' => 'Periksa Level Oli Mesin',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'kuras_isi_oli' => [
                        'nama' => 'Kuras dan Isi Oli Mesin',
                        'interval' => 400,
                        'satuan' => 'jam',
                    ],
                    'ganti_filter_oli' => [
                        'nama' => 'Ganti Filter Oli Mesin',
                        'interval' => 400,
                        'satuan' => 'jam',
                    ]
                ],
                'engine_speed_control' => [
                    'cek_sesuaikan_governor_harian' => [
                        'nama' => 'Cek dan Sesuaikan Tuas Governor dan Kontrol Kecepatan Engine',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'cek_sesuaikan_governor_250' => [
                        'nama' => 'Cek dan Sesuaikan Tuas Governor dan Kontrol Kecepatan Engine',
                        'interval' => 250,
                        'satuan' => 'jam',
                    ]
                ],
                'emission_control' => [
                    'periksa_injektor' => [
                        'nama' => 'Periksa, Bersihkan dan Tes Injektor Bahan Bakar Jika Diperlukan',
                        'interval' => 1500,
                        'satuan' => 'jam',
                    ],
                    'periksa_tes_egr' => [
                        'nama' => 'Periksa, Bersihkan dan Tes Katup EGR',
                        'interval' => 3000,
                        'satuan' => 'jam',
                    ],
                    'bersihkan_katup_egr' => [
                        'nama' => 'Bersihkan Katup Timah EGR',
                        'interval' => 3000,
                        'satuan' => 'jam',
                    ],
                    'output_voltage' => [
                        'nama' => 'Output Voltage',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ]
                ]
            ],
            'generator_engine_starboardside' => [
                'cooling_system' => [
                    'cek_coolant' => [
                        'nama' => 'Cek dan Isi Ulang Cairan Pendingin Mesin / Water Coolant',
                        'interval' => 1,
                        'satuan' => 'harian'
                    ],
                    'cek_bersihkan_core_heat_exchanger' => [
                        'nama' => 'Cek dan Bersihkan Core Heat Exchanger',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ],
                    'cek_sesuaikan_vbelt_water_pump_alternator_50' => [
                        'nama' => 'Cek dan Sesuaikan V-Belt Kipas Pendingin/Water Pump & Alternator',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ],
                    'cek_sesuaikan_vbelt_water_pump_alternator_250' => [
                        'nama' => 'Cek dan Sesuaikan V-Belt Kipas Pendingin/Water Pump & Alternator',
                        'interval' => 250,
                        'satuan' => 'jam'
                    ],
                    'kuras_isi_ulang_sistem_pendingin' => [
                        'nama' => 'Kuras dan Isi Ulang Sistem Pendingin dengan Pendingin Baru',
                        'interval' => 2000,
                        'satuan' => 'jam'
                    ]
                ],
                'cylinder_head' => [
                    'sesuaikan_jarak_bebas_katup' => [
                        'nama' => 'Sesuaikan Jarak Bebas Katup Hisap & Buang',
                        'interval' => 1000,
                        'satuan' => 'jam'
                    ]
                ],
                'electrical_equipment' => [
                    'periksa_indikator' => [
                        'nama' => 'Periksa Indikator',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'periksa_baterai_level' => [
                        'nama' => 'Periksa Baterai & Level Air Accu',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ]
                ],
                'engine_oil' => [
                    'periksa_level_oli' => [
                        'nama' => 'Periksa Level Oli Mesin',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'kuras_isi_oli' => [
                        'nama' => 'Kuras dan Isi Oli Mesin',
                        'interval' => 400,
                        'satuan' => 'jam'
                    ],
                    'ganti_filter_oli' => [
                        'nama' => 'Ganti Filter Oli Mesin',
                        'interval' => 400,
                        'satuan' => 'jam'
                    ]
                ],
                'engine_speed_control' => [
                    'cek_sesuaikan_governor_harian' => [
                        'nama' => 'Cek dan Sesuaikan Tuas Governor dan Kontrol Kecepatan Engine',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'cek_sesuaikan_governor_250' => [
                        'nama' => 'Cek dan Sesuaikan Tuas Governor dan Kontrol Kecepatan Engine',
                        'interval' => 250,
                        'satuan' => 'jam'
                    ]
                ],
                'emission_control_warranty' => [
                    'periksa_injektor' => [
                        'nama' => 'Periksa, Bersihkan dan Tes Injektor Bahan Bakar Jika Diperlukan',
                        'interval' => 1500,
                        'satuan' => 'jam'
                    ],
                    'periksa_turbocharger' => [
                        'nama' => 'Periksa Turbocharger (Cuci Blower Sepertinya)',
                        'interval' => 3000,
                        'satuan' => 'jam'
                    ],
                    'periksa_katup_egr' => [
                        'nama' => 'Periksa, Bersihkan dan Tes Katup EGR',
                        'interval' => 3000,
                        'satuan' => 'jam'
                    ],
                    'bersihkan_katup_timah_egr' => [
                        'nama' => 'Bersihkan Katup Timah EGR',
                        'interval' => 3000,
                        'satuan' => 'jam'
                    ],
                    'bersihkan_egr_pendingin' => [
                        'nama' => 'Bersihkan EGR Pendingin (Bersihkan dengan Menyemprot Saluran Air atau Udara)',
                        'interval' => 1500,
                        'satuan' => 'jam'
                    ],
                    'periksa_sistem_crankcase' => [
                        'nama' => 'Periksa Sistem Kinerja Crankcase',
                        'interval' => 1500,
                        'satuan' => 'jam'
                    ]
                ],
                'fuel' => [
                    'cek_tambah_bahan_bakar' => [
                        'nama' => 'Cek dan Tambahkan Bahan Bakar Diperlampungan',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'kuras_tangki_bahan_bakar' => [
                        'nama' => 'Kuras Tangki Bahan Bakar',
                        'interval' => 250,
                        'satuan' => 'jam'
                    ],
                    'keringkan_filter_separator' => [
                        'nama' => 'Keringkan Filter Bahan Bakar/Water Separator',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ],
                    'periksa_filter_separator' => [
                        'nama' => 'Periksa Filter Bahan Bakar/Water Separator',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'bersihkan_filter_separator' => [
                        'nama' => 'Bersihkan Filter Bahan Bakar/Water Separator',
                        'interval' => 500,
                        'satuan' => 'jam'
                    ],
                    'ganti_filter_bahan_bakar' => [
                        'nama' => 'Ganti Filter Bahan Bakar',
                        'interval' => 500,
                        'satuan' => 'jam'
                    ]
                ]
            ],
            'emergency_generator_engine' => [
                'cooling_system' => [
                    'cek_coolant' => [
                        'nama' => 'Cek dan Isi Ulang Cairan Pendingin Mesin',
                        'interval' => 1,
                        'satuan' => 'harian'
                    ],
                    'cek_bersihkan_sirip_radiator' => [
                        'nama' => 'Cek dan Bersihkan Sirip Radiator',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ],
                    'cek_sesuaikan_vbelt_kipas_50' => [
                        'nama' => 'Cek dan Sesuaikan V-Belt Kipas Pendingin',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ],
                    'cek_sesuaikan_vbelt_kipas_250' => [
                        'nama' => 'Cek dan Sesuaikan V-Belt Kipas Pendingin',
                        'interval' => 250,
                        'satuan' => 'jam'
                    ],
                    'kuras_sistem_pendingin' => [
                        'nama' => 'Kuras, Siram dan Isi Ulang Sistem Pendingin dengan Pendingin Baru',
                        'interval' => 2000,
                        'satuan' => 'jam'
                    ]
                ],
                'cylinder_head' => [
                    'sesuaikan_jarak_katup' => [
                        'nama' => 'Sesuaikan Jarak Bebas Katup Asupan/Buang',
                        'interval' => 1000,
                        'satuan' => 'jam'
                    ]
                ],
                'electrical_equipment' => [
                    'periksa_indikator' => [
                        'nama' => 'Periksa Indikator',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'periksa_baterai' => [
                        'nama' => 'Periksa Baterai',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ]
                ],
                'engine_oil' => [
                    'periksa_level_oli' => [
                        'nama' => 'Periksa Level Oli Mesin',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'kuras_isi_oli' => [
                        'nama' => 'Kuras dan Isi Oli Mesin',
                        'interval' => 500,
                        'satuan' => 'jam'
                    ],
                    'ganti_filter_oli' => [
                        'nama' => 'Ganti Filter Oli Mesin',
                        'interval' => 500,
                        'satuan' => 'jam'
                    ]
                ],
                'engine_speed_control' => [
                    'cek_sesuaikan_governor_harian' => [
                        'nama' => 'Cek dan Sesuai Tuas Governor dan Kontrol Kecepatan Engine',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'cek_sesuaikan_governor_250' => [
                        'nama' => 'Cek dan Sesuai Tuas Governor dan Kontrol Kecepatan Engine',
                        'interval' => 250,
                        'satuan' => 'jam'
                    ]
                ],
                'emission_control_warranty' => [
                    'periksa_injektor' => [
                        'nama' => 'Periksa, Bersihkan dan Tes Injektor Bahan Bakar Jika Diperlukan',
                        'interval' => 1500,
                        'satuan' => 'jam'
                    ],
                    'periksa_turbocharger' => [
                        'nama' => 'Periksa Turbocharger (Cuci Blower Sepertinya)',
                        'interval' => 3000,
                        'satuan' => 'jam'
                    ],
                    'periksa_katup_egr' => [
                        'nama' => 'Periksa, Bersihkan dan Tes Katup EGR',
                        'interval' => 3000,
                        'satuan' => 'jam'
                    ],
                    'bersihkan_katup_timah' => [
                        'nama' => 'Bersihkan Katup Timah EGR',
                        'interval' => 3000,
                        'satuan' => 'jam'
                    ],
                    'bersihkan_egr_pendingin' => [
                        'nama' => 'Bersihkan EGR Pendingin (Bersihkan dengan Menyemprot Saluran Air atau Udara)',
                        'interval' => 1500,
                        'satuan' => 'jam'
                    ],
                    'periksa_sistem_crankcase' => [
                        'nama' => 'Periksa Sistem Kinerja Crankcase',
                        'interval' => 1500,
                        'satuan' => 'jam'
                    ]
                ],
                'fuel' => [
                    'cek_tambah_bahan_bakar' => [
                        'nama' => 'Cek dan Tambahkan Bahan Bakar Diperlampungan',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'kuras_tangki_bahan_bakar' => [
                        'nama' => 'Kuras Tangki Bahan Bakar',
                        'interval' => 250,
                        'satuan' => 'jam'
                    ],
                    'keringkan_filter_separator' => [
                        'nama' => 'Keringkan Filter Bahan Bakar/Water Separator',
                        'interval' => 50,
                        'satuan' => 'jam'
                    ],
                    'periksa_filter_separator' => [
                        'nama' => 'Periksa Filter Bahan Bakar/Water Separator',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'bersihkan_filter_separator' => [
                        'nama' => 'Bersihkan Filter Bahan Bakar/Water Separator',
                        'interval' => 500,
                        'satuan' => 'jam'
                    ],
                    'ganti_filter_bahan_bakar' => [
                        'nama' => 'Ganti Filter Bahan Bakar',
                        'interval' => 500,
                        'satuan' => 'jam'
                    ]
                ],
                'hoses' => [
                    'ganti_hose_sistem' => [
                        'nama' => 'Ganti Hose Sistem Bahan Bakar dan Pendingin',
                        'interval' => 2000,
                        'satuan' => 'jam'
                    ]
                ],
                'complete_engine' => [
                    'pemeriksaan_visual' => [
                        'nama' => 'Pemeriksaan Visual Menyeluruh Setiap Hari',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'beroperasi_baik' => [
                        'nama' => 'Beroperasi dengan Baik',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ]
                ],
                'alternator' => [
                    'getaran_normal' => [
                        'nama' => 'Getaran Tidak Normal',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ],
                    'output_voltage' => [
                        'nama' => 'Output Voltage',
                        'interval' => 1,
                        'satuan' => 'harian',
                        'tipe_input' => 'date'
                    ]
                ]
            ],
            'gearbox_starboardside' => [
                'beroperasi_baik' => [
                    'nama' => 'Beroperasi dengan Baik',
                    'interval' => 1,
                    'satuan' => 'harian',
                    'tipe_input' => 'date'
                ],
                'level_oli' => [
                    'nama' => 'Level Oli (antara "MIN" atau "MAX" dan Kondisi Oli)',
                    'interval' => 100,
                    'satuan' => 'jam'
                ],
                'kebocoran_oli_air' => [
                    'nama' => 'Memeriksa Kebocoran Oli dan Air',
                    'interval' => null,
                    'satuan' => null
                ],
                'membersihkan_bagian' => [
                    'nama' => 'Membersihkan Bagian-Bagian dan Pipa',
                    'interval' => 10000,
                    'satuan' => 'jam'
                ],
                'berjalan_tidak_normal' => [
                    'nama' => 'Berjalan dengan Tidak Normal',
                    'interval' => 500,
                    'satuan' => 'jam'
                ],
                'suara_tidak_normal' => [
                    'nama' => 'Suara Tidak Normal',
                    'interval' => null,
                    'satuan' => null
                ],
                'mengencangkan_torsi' => [
                    'nama' => 'Mengencangkan Torsi Baut Baut',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'fire_system' => [
                'berjalan_baik' => [
                    'nama' => 'Berjalan dengan Baik',
                    'interval' => 1,
                    'satuan' => 'harian',
                    'tipe_input' => 'date'
                ],
                'memeriksa_terminal' => [
                    'nama' => 'Memeriksa Terminal, Kabel dan Motor Pompa Pemadam Kebakaran',
                    'interval' => null,
                    'satuan' => null
                ],
                'membersihkan_kipas' => [
                    'nama' => 'Membersihkan Kipas, Impeller dan Ventilasi Pelindung Utama Motor Pemadam Kebakaran',
                    'interval' => null,
                    'satuan' => null
                ],
                'membersihkan_selang' => [
                    'nama' => 'Membersihkan Selang Pemadam Kebakaran',
                    'interval' => null,
                    'satuan' => null
                ],
                'suara_getaran_normal' => [
                    'nama' => 'Suara dan Getaran Tidak Normal',
                    'interval' => null,
                    'satuan' => null
                ],
                'memeriksa_sistem' => [
                    'nama' => 'Memeriksa Sistem Selang Utama Kebakaran Agar Tidak Bocor',
                    'interval' => null,
                    'satuan' => null
                ],
                'buka_tutup_katup' => [
                    'nama' => 'Buka dan Tutup Semua Katup di Utama, Pastikan Semua Katup Berjalan dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'fire_pump' => [
                'berjalan_baik' => [
                    'nama' => 'Berjalan dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ],
                'suara_getaran' => [
                    'nama' => 'Suara dan Getaran Tidak Normal',
                    'interval' => null,
                    'satuan' => null
                ],
                'kebocoran_blok' => [
                    'nama' => 'Kebocoran dari Blok Pemadam',
                    'interval' => null,
                    'satuan' => null
                ],
                'pelumasan_gemuk' => [
                    'nama' => 'Pelumasan Gemuk',
                    'interval' => null,
                    'satuan' => null
                ],
                'katup_berjalan' => [
                    'nama' => 'Semua Katup Berjalan Normal',
                    'interval' => null,
                    'satuan' => null
                ],
                'selang_siap' => [
                    'nama' => 'Selang Siap untuk Digunakan',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'emergency_fire_pump' => [
                'berjalan_baik' => [
                    'nama' => 'Berjalan dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ],
                'kebocoran_blok' => [
                    'nama' => 'Kebocoran dari Blok Pemadam',
                    'interval' => null,
                    'satuan' => null
                ],
                'katup_berjalan' => [
                    'nama' => 'Semua Katup Berjalan Normal',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'ballast_system' => [
                'tidak_ada_karat' => [
                    'nama' => 'Tidak Ada Karat dan Kebocoran',
                    'interval' => null,
                    'satuan' => null
                ],
                'katup_berjalan' => [
                    'nama' => 'Semua Katup Berjalan Normal',
                    'interval' => null,
                    'satuan' => null
                ],
                'selang_siap' => [
                    'nama' => 'Selang Siap untuk Digunakan',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'bilge_gs_system' => [
                'lambung_pemadam' => [
                    'nama' => 'Lambung dan Pemadam GS Berjalan dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ],
                'tidak_ada_karat_dan_kebocoran' => [
                    'nama' => 'Tidak Ada Karat dan Kebocoran',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'emergency_steering' => [
                'beroperasi_baik' => [
                    'nama' => 'Beroperasi dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'control_consol' => [
                'beroperasi_baik' => [
                    'nama' => 'Beroperasi dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'cleanliness_er' => [
                'bersih_tanpa_sampah' => [
                    'nama' => 'Harus Bersih Tanpa Sampah atau Limbah Minyak',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'quick_closing_valves' => [
                'beroperasi_baik' => [
                    'nama' => 'Beroperasi dengan Baik',
                    'interval' => null,
                    'satuan' => null
                ],
                'katup_berjalan' => [
                    'nama' => 'Semua Katup Berjalan Normal',
                    'interval' => null,
                    'satuan' => null
                ]
            ],
            'checklist_engine_situasional' => [
                'penggantian_oli_powerpack' => [
                    'nama' => 'Penggantian Oli Powerpack',
                    'interval' => 800,
                    'satuan' => 'jam',
                    'keterangan' => '2x Oli Ganti Mesin = 1x Ganti Oli Powerpack'
                ]
            ]
        ];
    }

    public function storeResult(Request $request)
    {
        try {
            $request->validate([
                'kapal_id' => 'required',
                'item_id' => 'required',
                'kondisi' => 'required|in:bagus,rusak',
                'keterangan' => 'nullable|string',
                'history_id' => 'nullable'
            ]);

            // Ambil history perjalanan yang akan digunakan
            $historyId = $request->history_id;
            if (!$historyId) {
                // Jika tidak ada history_id, ambil yang terbaru
                $latestHistory = DB::table('histori_perjalanan_kapal')
                    ->where('kapal_id', $request->kapal_id)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if (!$latestHistory) {
                    return redirect()->back()->with('error', 'Tidak ada history perjalanan untuk kapal ini');
                }
                $historyId = $latestHistory->id;
            }

            // Simpan data pemeriksaan ke tabel hasil_pemeriksaan_engine
            DB::table('hasil_pemeriksaan_engine')->insert([
                'kapal_id' => $request->kapal_id,
                'history_id' => $historyId,
                'item_id' => $request->item_id,
                'kondisi' => $request->kondisi,
                'keterangan' => $request->keterangan ?? '-',
                'tanggal_pemeriksaan' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return redirect()->back()->with('success', 'Data pemeriksaan berhasil disimpan');
        } catch (\Exception $e) {
            \Log::error('Error in storeResult:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return redirect()->back()->with('error', 'Gagal menyimpan data pemeriksaan: ' . $e->getMessage());
        }
    }

    public function storeItemPemeriksaan(Request $request)
    {
        try {
            $request->validate([
                'item_id' => 'required',
                'kondisi' => 'required|in:bagus,tidak',
                'keterangan' => 'nullable|string',
                'tanggal_pemeriksaan' => 'required|date',
                'kapal_id' => 'required'
            ]);

            // Decode kapal hash id
            $kapalId = HashIdHelper::decode($request->kapal_id);

            // Ambil history perjalanan terbaru dari kapal
            $latestHistory = DB::table('histori_perjalanan_kapal')
                ->where('kapal_id', $kapalId)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$latestHistory) {
                return response()->json([
                    'status' => false,
                    'message' => 'Tidak ada history perjalanan untuk kapal ini'
                ], 404);
            }

            // Simpan data pemeriksaan ke tabel hasil_pemeriksaan_engine
            DB::table('hasil_pemeriksaan_engine')->insert([
                'kapal_id' => $kapalId,
                'history_id' => $latestHistory->id,
                'item_id' => $request->item_id,
                'kondisi' => $request->kondisi,
                'keterangan' => $request->keterangan ?? '-', // Set default value jika null
                'tanggal_pemeriksaan' => $request->tanggal_pemeriksaan,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Data pemeriksaan berhasil disimpan'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in storeItemPemeriksaan:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'status' => false,
                'message' => 'Gagal menyimpan data pemeriksaan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function export($hashedId, Request $request)
    {
        try {
            $id = HashIdHelper::decode($hashedId);
            $tujuan = $request->tujuan;
            
            $kapal = Kapal::findOrFail($id);
            
            $hasilPemeriksaan = HasilPemeriksaanEngine::from('hasil_pemeriksaan_engine as hpe')
                ->select('hpe.*', 'cde.item_pemeriksaan', 'hpk.tujuan', 'hpk.rh_me_ps', 'hpk.rh_me_sb')
                ->leftJoin('checklist_data_engine as cde', 'hpe.item_id', '=', 'cde.id')
                ->leftJoin('histori_perjalanan_kapal as hpk', 'hpe.history_id', '=', 'hpk.id')
                ->where('hpe.kapal_id', $id)
                ->where('hpk.tujuan', $tujuan)
                ->orderBy('hpe.tanggal_pemeriksaan', 'desc')
                ->get();

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set header
            $sheet->setCellValue('A1', 'No');
            $sheet->setCellValue('B1', 'Tanggal');
            $sheet->setCellValue('C1', 'Item Pemeriksaan');
            $sheet->setCellValue('D1', 'Kondisi');
            $sheet->setCellValue('E1', 'Keterangan');
            $sheet->setCellValue('F1', 'Running Hours ME PS');
            $sheet->setCellValue('G1', 'Running Hours ME SB');

            // Isi data
            $row = 2;
            foreach ($hasilPemeriksaan as $index => $hasil) {
                $sheet->setCellValue('A' . $row, $index + 1);
                $sheet->setCellValue('B' . $row, Carbon::parse($hasil->tanggal_pemeriksaan)->format('d/m/Y H:i'));
                $sheet->setCellValue('C' . $row, $hasil->item_pemeriksaan);
                $sheet->setCellValue('D' . $row, ucfirst($hasil->kondisi));
                $sheet->setCellValue('E' . $row, $hasil->keterangan);
                $sheet->setCellValue('F' . $row, number_format($hasil->rh_me_ps));
                $sheet->setCellValue('G' . $row, number_format($hasil->rh_me_sb));
                $row++;
            }

            // Auto size columns
            foreach (range('A', 'G') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Create file
            $writer = new Xlsx($spreadsheet);
            $filename = "Hasil_Pemeriksaan_Engine_{$kapal->nama_kapal}_{$tujuan}_" . date('Y-m-d_H-i-s') . ".xlsx";
            
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');
            
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            \Log::error('Error in export:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return back()->with('error', 'Gagal mengexport data: ' . $e->getMessage());
        }
    }
} 